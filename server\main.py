from __future__ import annotations

import os
import traceback
from typing import Any, Dict

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

from rede.api.contracts import (
    CalculateScenarioRequest,
    CalculateScenarioResponse,
    KPIBlock,
)
from rede.engine.context import EngineContext
from rede.engine.config_loader import ConfigAdapter, LoadedConfig
from rede.engine.timeline import TimelineEngine
from rede.models.core import Portfolio


APP_VERSION = "v1.0-fastapi-stub"


def _load_configs_from_env() -> ConfigAdapter:
    """
    Reads config file paths from environment variables. All are optional.
    If a file is missing or path is empty, the adapter still returns a LoadedConfig
    with empty content (the engine uses safe defaults).
    """
    state = os.getenv("STATE_RULES_PATH", "")
    city = os.getenv("CITY_BENCHMARKS_PATH", "")
    tax  = os.getenv("TAX_LAWS_PATH", "")
    rate = os.getenv("RATE_INDEX_PATH", "")
    bank = os.getenv("BANK_POLICIES_PATH", "")
    return ConfigAdapter(state, city, tax, rate, bank)


def _ctx_from_adapter(adapter: ConfigAdapter) -> EngineContext:
    """
    Build an EngineContext using the LoadedConfig objects from the adapter.
    """
    ctx = EngineContext(
        state_rules=adapter.state_rules,
        city_benchmarks=adapter.city_benchmarks,
        tax_laws_fy=adapter.tax_laws,
        rate_index_path=adapter.rate_index,
        bank_policies=adapter.bank_policies,
    )
    # Populate simple SHA panel for API consumers
    ctx.config_hashes.state_rules_sha256 = adapter.state_rules.sha256 or ""
    ctx.config_hashes.city_benchmarks_sha256 = adapter.city_benchmarks.sha256 or ""
    ctx.config_hashes.tax_laws_fy_sha256 = adapter.tax_laws.sha256 or ""
    ctx.config_hashes.rate_index_path_sha256 = adapter.rate_index.sha256 or ""
    ctx.config_hashes.bank_policies_sha256 = adapter.bank_policies.sha256 or ""
    return ctx


def _to_response(res: Dict[str, Any], ctx: EngineContext) -> CalculateScenarioResponse:
    """
    Map the engine's result() dict to the public API schema.
    """
    kpi_src = res.get("kpis", {}) or {}
    kpis = KPIBlock(**kpi_src)  # fields align (realized_xirr, mtm_xirr, min_liquid_balance, etc.)

    # Keep both keys for funding breakdowns; some clients prefer one name.
    funding_panel = res.get("funding_panel", {}) or res.get("funding_waterfall", {}) or {}
    funding_waterfall = funding_panel

    tax_panel = res.get("tax_panel", {})
    liquidity_panel = res.get("liquidity_panel", {})
    exit_sensitivity = res.get("exit_sensitivity", [])
    narratives = res.get("narrative_summary", [])
    key_events = res.get("key_events", [])
    risk_flags = res.get("risk_flags", [])
    ledger = res.get("ledger", [])

    cfg_hashes = {
        "state_rules": ctx.config_hashes.state_rules_sha256,
        "city_benchmarks": ctx.config_hashes.city_benchmarks_sha256,
        "tax_laws": ctx.config_hashes.tax_laws_fy_sha256,
        "rate_index": ctx.config_hashes.rate_index_path_sha256,
        "bank_policies": ctx.config_hashes.bank_policies_sha256,
    }

    return CalculateScenarioResponse(
        kpis=kpis,
        liquidity_panel=liquidity_panel,
        funding_panel=funding_panel,
        funding_waterfall=funding_waterfall,
        tax_panel=tax_panel,
        exit_sensitivity=exit_sensitivity,
        narrative_summary=narratives,
        key_events=key_events,
        risk_flags=risk_flags,
        ledger=ledger,
        config_hashes=cfg_hashes,
    )


# --------------------------
# FastAPI app
# --------------------------

app = FastAPI(title="Real Estate Decision Engine API", version=APP_VERSION)

# CORS: open by default; tighten to your domains in deployment
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("CORS_ALLOW_ORIGINS", "*").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class ErrorOut(BaseModel):
    detail: str
    trace: str | None = None


@app.get("/health")
def health() -> Dict[str, str]:
    return {"status": "ok", "version": APP_VERSION}


@app.get("/config/hashes")
def config_hashes() -> Dict[str, str]:
    adapter = _load_configs_from_env()
    return {
        "state_rules": adapter.state_rules.sha256 or "",
        "city_benchmarks": adapter.city_benchmarks.sha256 or "",
        "tax_laws": adapter.tax_laws.sha256 or "",
        "rate_index": adapter.rate_index.sha256 or "",
        "bank_policies": adapter.bank_policies.sha256 or "",
    }


@app.post("/calculate_scenario", response_model=CalculateScenarioResponse, responses={400: {"model": ErrorOut}, 500: {"model": ErrorOut}})
def calculate_scenario(req: CalculateScenarioRequest):
    try:
        portfolio = Portfolio.parse_obj(req.portfolio)

        adapter = _load_configs_from_env()
        ctx = _ctx_from_adapter(adapter)

        eng = TimelineEngine(ctx)
        eng.ctx.portfolio = portfolio
        eng.run()
        res = eng.result()

        return _to_response(res, ctx)

    except Exception as e:
        # In dev, return trace; in prod, set REDUCE_ERROR_TRACE=1 to hide it
        reduce_trace = os.getenv("REDUCE_ERROR_TRACE", "0") == "1"
        trace = None if reduce_trace else traceback.format_exc()
        raise HTTPException(status_code=400, detail=ErrorOut(detail=str(e), trace=trace).dict())


# Optional stub endpoint: sensitivity runner (Add-On A)
@app.post("/sensitivity/run", responses={501: {"model": ErrorOut}})
def sensitivity_runner_stub():
    raise HTTPException(status_code=501, detail=ErrorOut(detail="Sensitivity Runner not wired yet. Use /calculate_scenario.", trace=None).dict())
