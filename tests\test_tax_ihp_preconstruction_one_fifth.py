from decimal import Decimal as D
from datetime import date

from rede.engine.tax_engine import TaxEngine, fy_label
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy

def _ctx_pf_for_tax_only():
    class _Cfg: content = {
        "ihp": {"self_occupied_interest_cap_per_person": 200000, "hp_loss_setoff_cap_per_person": 200000, "carry_forward_years": 8},
        "section80c": {"cap_per_person": 150000},
        "cii": {"FY2024-25": 348, "FY2025-26": 360}
    }
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    p = Person(id="p1", name="A", tax_regime="old", shareholdings={}, liquid_cash=D(0))
    prop = Property(id="prop1", city="BLR",
                    heads=PropertyHeads(base_price_ex_gst=D(0)),
                    plan=PaymentPlan(events=[]),
                    ownership_costs=PropertyOwnershipCosts(),
                    occupancy=PropertyOccupancy())
    pf = Portfolio(currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=24,
                   persons=[p], properties=[prop],
                   mf_holdings=[], user_starting_liquid_cash_by_person={"p1": D(0)},
                   alt_invest_return_after_tax_pct=D(6), loan_sanction_risk_pct=D(0),
                   funding_strategy=FundingStrategy(), tax_settings_global=TaxSettingsGlobal(),
                   configs=RuntimeConfigs("","","","",""))
    return ctx, pf, prop

def test_preconstruction_interest_1_5_allocation():
    ctx, pf, prop = _ctx_pf_for_tax_only()
    tax = TaxEngine(ctx, pf)
    # Pre-EMI (pre-OC) interest: 10,000 for 6 months (FY2025-26)
    fy = "FY2025-26"
    for m in range(6):
        d = date(2025, 4 + m, 5)
        tax.accrue_interest(prop, d, D(10000), is_preconstruction=True, is_letout=False)
    # OC in Sep 2025 -> first eligible FY is FY2025-26
    tax.set_preconstruction_start_on_oc(prop, date(2025, 9, 15))
    # Settle FY
    tax.settle_fy(fy, ledger=type("L", (), {"lines": []})())  # pass a dummy ledger with .lines

    panel = tax.get_panel()
    # Total precon = 60,000; 1/5 per eligible FY = 12,000
    assert abs(panel["ihp"]["selfocc_total_interest_allowed"] - 12000.0) < 1e-6
