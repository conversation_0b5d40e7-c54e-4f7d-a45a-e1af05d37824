from pathlib import Path
from decimal import Decimal as D

import pytest

from tests.util_json import load_portfolio_from_json
from tests.acceptance.helpers import engine_with_tax

FIXTURE_DIR = Path(__file__).parent.parent / "fixtures"

ALL_FIXTURES = [
    "gs1_uc_subvention_oc_rent_movein.json",
    "gs2_uc_letout_194ib.json",
    "gs3_resale_existingbasis_sale_m36.json",
    "gs4_sell_one_buy_two.json",
    "gs5_rate_shocks.json",
    "gs6_liquidity_stress.json",
]

@pytest.mark.parametrize("fname", ALL_FIXTURES)
def test_fixture_runs_and_core_contract(fname):
    pf = load_portfolio_from_json(str(FIXTURE_DIR / fname))
    eng = engine_with_tax()
    eng.ctx.portfolio = pf
    eng.run()
    res = eng.result()

    # Schema keys
    for k in ("kpis", "ledger", "narrative_summary", "risk_flags"):
        assert k in res, f"Missing {k} in result for {fname}"

    # Ledger must be chronological and non-empty for most scenarios
    assert isinstance(res["ledger"], list)
    assert all("date" in l and "label" in l and "amount" in l for l in res["ledger"])

    # KPIs basic sanity
    k = res["kpis"]
    assert "total_outflows" in k and "total_inflows" in k and "net_cash" in k
    assert "realized_xirr" in k and "mtm_xirr" in k

def test_specific_assertions_across_fixtures():
    # Run each and assert targeted behaviors from our golden scenarios
    def run(fname):
        pf = load_portfolio_from_json(str(FIXTURE_DIR / fname))
        eng = engine_with_tax(); eng.ctx.portfolio = pf; eng.run()
        return eng.result()

    # S1: subvention + OC gating + rent paid/saved
    s1 = run("gs1_uc_subvention_oc_rent_movein.json")
    assert any("OC received" in s for s in s1["narrative_summary"])
    assert any("Rent Paid" in l["label"] for l in s1["ledger"])
    assert any("Rent Saved" in l["label"] for l in s1["ledger"])
    assert not any("Pre-EMI" in l["label"] for l in s1["ledger"])

    # S2: let-out + potential TDS credits path
    s2 = run("gs2_uc_letout_194ib.json")
    assert any(l for l in s2["ledger"] if l["category"] == "rent_received")
    assert "tds_credits_accumulated" in s2["kpis"]

    # S3: resale sale with CGT/brokerage/transfer
    s3 = run("gs3_resale_existingbasis_sale_m36.json")
    labs = [l["label"] for l in s3["ledger"]]
    assert any("Sale Proceeds" in x for x in labs)
    assert any("Brokerage on Sale" == x for x in labs)
    assert any("Transfer Fee" == x for x in labs)
    assert any("Property CGT" in x for x in labs)

    # S4: sell-one-buy-two: should show sale + builder payments
    s4 = run("gs4_sell_one_buy_two.json")
    assert any("Sale Proceeds" in l["label"] for l in s4["ledger"])
    assert any("Builder Payment" in l["label"] for l in s4["ledger"])

    # S5: rate shocks present in narrative and EMIs exist
    s5 = run("gs5_rate_shocks.json")
    assert any("Loan rate shock applied" in s for s in s5["narrative_summary"])
    assert any(l for l in s5["ledger"] if l["category"] == "emi")

    # S6: liquidity stress — MF redemption and min liquid ≤ 0
    s6 = run("gs6_liquidity_stress.json")
    assert any(l for l in s6["ledger"] if l["category"] == "funding_mf")
    assert s6["kpis"]["min_liquid_balance"] <= 0
