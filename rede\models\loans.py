from pydantic import BaseModel, Field
from ..types import Money, Percent, D
from .enums import LoanProductType, RateIndex, RateChangePolicy

class PrepaymentPolicy(BaseModel):
    mode: str = Field(default="TenureReduction")  # or "EMIReduction"
    spread_threshold_pct: Percent = D(0)  # ROI - AltReturn >= threshold → prepay
    min_buffer_months: int = 6

class LTVBasis(BaseModel):
    exclude_gst_from_base: bool = True
    ltv_pct: Percent = Field(default=D(80))

class RateShock(BaseModel):
    month: int
    new_index_pct: Percent

class BalanceTransferEvent(BaseModel):
    month: int
    new_roi_pct: Percent
    fee_pct: Percent | None = None
    fee_fixed: Money | None = None

class TopUpEvent(BaseModel):
    month: int
    amount: Money
    roi_pct: Percent

class LoanInsuranceMRTA(BaseModel):
    premium_payment: str  # "UpfrontBundled" | "Annual"
    upfront_premium: Money | None = None
    annual_premium: Money | None = None

class BaseLoan(BaseModel):
    product_type: LoanProductType
    expected_sanctioned_amount: Money
    final_sanctioned_amount: Money | None = None
    sanction_validity_months: int = 6

    ltv_basis: LTVBasis

    initial_roi_annual_pct: Percent
    rate_index: RateIndex
    spread_pct: Percent = D(0)
    reset_frequency_months: int = 3
    rate_shocks: list[RateShock] = []
    on_rate_change_policy: RateChangePolicy = RateChangePolicy.ADJUST_TENURE

    tenure_months: int
    pre_emi_till_possession: bool = True
    emi_start_month: int | None = None

    processing_fee_pct: Percent = D(0)
    processing_fee_gst_pct: Percent = D(18)
    valuation_legal_charges: Money = D(0)
    mod_franking_pct: Percent = D(0.2)

    prepayment_policy: PrepaymentPolicy = PrepaymentPolicy()
    prepayment_penalty_pct_if_applicable: Percent = D(0)

    balance_transfer_events: list[BalanceTransferEvent] = []
    topup_events: list[TopUpEvent] = []
    loan_insurance: LoanInsuranceMRTA | None = None

class TermLoan(BaseLoan):
    product_type: LoanProductType = LoanProductType.TERM

class OverdraftLoan(BaseLoan):
    product_type: LoanProductType = LoanProductType.OD
    od_sweep_enabled: bool = True
