from __future__ import annotations
from pydantic import BaseModel, Field
from datetime import date
from ..types import Money

class LedgerLine(BaseModel):
    date: date
    label: str
    category: str  # purchase, preemi, emi, maintenance, rent_paid, rent_received, tax, tds, funding_liquid, funding_mf, funding_od, funding_hl_topup, sale_in, sale_cost, loan_closure, cgt, other
    amount: Money   # outflows negative; inflows positive
    property_id: str | None = None
    person_id: str | None = None
    reason: str | None = None
    formula_hint: str | None = None
    inputs_used_json: dict | None = None

class Ledger(BaseModel):
    lines: list[LedgerLine] = Field(default_factory=list)
