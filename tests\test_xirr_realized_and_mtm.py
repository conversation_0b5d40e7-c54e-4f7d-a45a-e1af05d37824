from decimal import Decimal as D
from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PaymentEvent, PaymentTrigger, PropertyOwnershipCosts, PropertyOccupancy, ExitSettings
from rede.models.enums import PaymentTriggerType
from rede.types import D as DD

def _ctx_basic_tax():
    class _Cfg: content = {
        "tds_rules": {"rent_194IB": {"monthly_threshold": 50000, "rate_pct": 5}},
        "mf_tax": {"equity": {"holding_days_ltcg": 365, "stcg_rate_pct": 15, "ltcg_rate_pct": 10, "ltcg_annual_exemption": 100000, "indexation_allowed": False}},
        "property_cgt": {"stcg_rate_pct": 30, "ltcg_rate_pct": 20, "holding_days_ltcg": 730},
        "cii": {"FY2024-25": 348, "FY2025-26": 360}
    }
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg(); return ctx

def test_xirr_keys_present_and_positive_when_profitable():
    ctx = _ctx_basic_tax()
    p = Person(id="p1", name="Owner", tax_regime="old", shareholdings={}, liquid_cash=DD(200000))
    # Simple: base=180000 at t=0 (paid by cash), appreciation via city benchmarks handled by price engine (assume flat),
    # force sale at month 12 with assumed market value set via heads (we'll proxy by exit logic using current_value flow)
    ev = PaymentEvent(
        name="Buy", stage="Booking",
        trigger=PaymentTrigger(type=PaymentTriggerType.month, month=0),
        percent_of_base=DD(100), gst_pct=DD(0), financeable_by_bank=False
    )
    prop = Property(
        id="propX", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(180000)),
        plan=PaymentPlan(events=[ev]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(),
        exit=ExitSettings(sale_month=12, brokerage_pct=DD(1), buyer_tds_pct=DD(1))
    )
    pf = Portfolio(currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=12,
                   persons=[p], properties=[prop], mf_holdings=[],
                   user_starting_liquid_cash_by_person={"p1": DD(200000)},
                   alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
                   funding_strategy=FundingStrategy(od_limit=DD(0), mf_fifo_enabled=False),
                   tax_settings_global=TaxSettingsGlobal(),
                   configs=RuntimeConfigs("","","","",""))
    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf
    eng.run()
    k = eng.result()["kpis"]
    # We don’t know exact return (depends on price engine); just assert keys exist and XIRRs are numbers or None.
    assert "realized_xirr" in k and "mtm_xirr" in k
    # Given a sale, realized_xirr should be a number (not None)
    assert k["realized_xirr"] is not None
