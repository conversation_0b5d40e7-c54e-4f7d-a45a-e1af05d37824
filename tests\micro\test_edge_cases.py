from decimal import Decimal as D
from rede.types import D as DD
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy
from tests.acceptance.helpers import run_engine

def _base_pf():
    p = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(1000000))
    prop = Property(
        id="T", city="Mumbai",
        heads=PropertyHeads(base_price_ex_gst=DD(10000000), tds_buy_pct=DD(1)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy()
    )
    return p, prop

def _pf_for_rent_tds(city, value, yield_pct):
    p = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(500000))
    prop = Property(
        id="TDS", city=city,
        heads=PropertyHeads(base_price_ex_gst=DD(value), tds_buy_pct=DD(1)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(
            let_out_month=0, move_in_month=None,
            rent_yield_pct_of_value=DD(yield_pct), vacancy_pct=DD(0), collection_efficiency_pct=DD(100),
            pm_fee_monthly=DD(0), deposit_months=DD(0)
        )
    )
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=6,
        persons=[p], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(500000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(), tax_settings_global=TaxSettingsGlobal(),
        configs=RuntimeConfigs("","","","","")
    )
    return pf

def test_rent_tds_threshold_edge():
    # Scenario A: monthly gross rent just BELOW ₹50,000 -> no TDS credit
    # Rent = value * yield% / 12
    # Choose value so that rent ≈ 49,000: value=19,600,000 at 3% => 588,000/yr => 49,000/mo
    pf_low = _pf_for_rent_tds("Mumbai", value=19_600_000, yield_pct=3.0)
    res_low = run_engine(pf_low)
    assert abs(res_low["kpis"]["tds_credits_accumulated"]) == 0, "TDS credit should be 0 below threshold"

    # Scenario B: monthly gross rent just ABOVE ₹50,000 -> TDS credit accrues
    # value=20,200,000 at 3% => 606,000/yr => 50,500/mo (> 50,000)
    pf_high = _pf_for_rent_tds("Mumbai", value=20_200_000, yield_pct=3.0)
    res_high = run_engine(pf_high)
    assert res_high["kpis"]["tds_credits_accumulated"] > 0, "TDS credit should accrue above threshold"

def test_od_utilisation_risk_flag():
    p, prop = _base_pf()
    pf = Portfolio(currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=12,
                   persons=[p], properties=[prop], mf_holdings=[],
                   user_starting_liquid_cash_by_person={"u": DD(0)},  # force OD
                   alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
                   funding_strategy=FundingStrategy(od_limit=DD(200000), od_annual_rate_pct=DD(12)),
                   tax_settings_global=TaxSettingsGlobal(),
                   configs=RuntimeConfigs("","","","",""))
    res = run_engine(pf)
    assert any("OD utilisation reached ≥80%" in rf for rf in res["risk_flags"]), \
        "Expected OD utilisation ≥80% risk flag"

def test_hl_topup_risk_flag_presence_when_used():
    from rede.models.property import PaymentEvent, PaymentTrigger
    from rede.models.enums import PaymentTriggerType
    p = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(10000))
    ev = PaymentEvent(name="Big", stage="Poss",
                      trigger=PaymentTrigger(type=PaymentTriggerType.month, month=6),
                      percent_of_base=DD(90), gst_pct=DD(0), financeable_by_bank=True)
    prop = Property(id="S", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(2_000_000), ltv_pct=DD(70), tds_buy_pct=DD(1)),
        plan=PaymentPlan(events=[ev]), ownership_costs=PropertyOwnershipCosts(), occupancy=PropertyOccupancy()
    )
    pf = Portfolio(currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=12,
                   persons=[p], properties=[prop], mf_holdings=[],
                   user_starting_liquid_cash_by_person={"u": DD(10000)},
                   alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
                   funding_strategy=FundingStrategy(od_limit=DD(10000), od_annual_rate_pct=DD(12), allow_hl_topup=True),
                   tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","",""))
    res = run_engine(pf)
    assert any("Relied on HL Top-up" in rf for rf in res["risk_flags"]), \
        "Expected explicit HL Top-up reliance risk flag"
