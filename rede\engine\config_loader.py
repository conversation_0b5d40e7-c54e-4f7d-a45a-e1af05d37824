from __future__ import annotations
import json, hashlib
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, Tuple

# Simple loader + SHA helper used by EngineContext and CLI/test harness

def _load_json(path_str: str) -> Dict[str, Any]:
    if not path_str:
        return {}
    p = Path(path_str)
    if not p.exists():
        return {}
    return json.loads(p.read_text(encoding="utf-8"))

def _sha256_or_blank(path_str: str) -> str:
    if not path_str:
        return ""
    p = Path(path_str)
    if not p.exists():
        return ""
    h = hashlib.sha256()
    with p.open("rb") as f:
        for chunk in iter(lambda: f.read(8192), b""):
            h.update(chunk)
    return h.hexdigest()

@dataclass
class LoadedConfig:
    path: str | None
    content: Dict[str, Any]
    sha256: str | None

class ConfigAdapter:
    """
    Thin loader for external JSON configs:
    - state_rules.json
    - city_benchmarks.json
    - tax_laws.json (includes CII, MF rules, property CGT rules, etc.)
    - rate_index.json (optional, if you later wire external indices)
    - bank_policies.json
    """
    def __init__(self, state_path: str, city_path: str, tax_path: str, rate_path: str, bank_path: str):
        self.state_rules = LoadedConfig(state_path, _load_json(state_path), _sha256_or_blank(state_path))
        self.city_benchmarks = LoadedConfig(city_path, _load_json(city_path), _sha256_or_blank(city_path))
        self.tax_laws = LoadedConfig(tax_path, _load_json(tax_path), _sha256_or_blank(tax_path))
        self.rate_index = LoadedConfig(rate_path, _load_json(rate_path), _sha256_or_blank(rate_path))
        self.bank_policies = LoadedConfig(bank_path, _load_json(bank_path), _sha256_or_blank(bank_path))
