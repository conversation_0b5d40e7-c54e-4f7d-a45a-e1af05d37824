from __future__ import annotations
from pydantic import BaseModel, Field, validator
from typing import Dict, Literal
from decimal import Decimal

Percent = Decimal

class StampDutyRule(BaseModel):
    # Either a flat percent or a slabbed rule (future extension)
    stamp_duty_pct: Percent = Decimal("5.0")
    registration_fee_pct: Percent = Decimal("1.0")
    cess_pct: Percent = Decimal("0.0")
    tds_buy_pct: Percent = Decimal("1.0")  # Sec 194-IA default (engine also allows override per property)
    khata_or_mutation_fee_pct: Percent = Decimal("0.0")  # state-level default (city/society may add)

class StateRules(BaseModel):
    """
    Mapping of state -> generic stamp duty rules (can be overridden by city project).
    """
    states: Dict[str, StampDutyRule] = Field(default_factory=dict)

def parse_state_rules(content: dict) -> StateRules:
    """
    Validate a loaded state_rules.json dict.
    """
    if not isinstance(content, dict):
        content = {}
    # Accept either top-level {"states": {...}} or plain {"KA": {...}}
    if "states" not in content:
        content = {"states": content}
    return StateRules.parse_obj(content)
