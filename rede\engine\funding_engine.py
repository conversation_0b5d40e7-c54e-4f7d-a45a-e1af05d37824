from __future__ import annotations
from decimal import Decimal
from datetime import date
from typing import Dict, List, Tuple

from ..types import Money, D
from ..engine.ledger import Ledger, LedgerLine
from ..engine.context import EngineContext
from ..models.property import Property
from ..models.core import MFHoldingLot
from ..utils.money import pct_to_decimal


class FundingEngine:
    """
    v1.4 §7: Funding waterfall orchestrator.
      Order (current): Liquid -> MF FIFO -> OD -> HL Top-up

    Also:
      - Tracks liquid balance, OD balance
      - Accrues OD interest for month t to be posted as an outflow in month t+1
      - Tracks min liquid and peak OD for liquidity KPIs
      - Logs MF redemption lots for TaxEngine (CGT at FY settlement)
    """

    def __init__(self, ctx: EngineContext):
        self.ctx = ctx
        pf = ctx.portfolio

        # Initialize pooled liquid from persons
        if pf.user_starting_liquid_cash_by_person:
            self.liquid_bal = sum((D(v) for v in pf.user_starting_liquid_cash_by_person.values()), D(0))
        else:
            self.liquid_bal = D(0)

        self.od_bal = D(0)
        self.od_interest_due_next_month = D(0)

        self.min_liquid = self.liquid_bal
        self.min_liquid_month = 0
        self.peak_od = D(0)
        self.peak_od_month = 0

        # MF holdings FIFO queue (sorted by acquired_date)
        self.mf_fifo: List[MFHoldingLot] = sorted(pf.mf_holdings, key=lambda x: x.acquired_date)
        # (lot_id, units_sold, proceed_gross, exit_load, posting_date)
        self.mf_redemptions_log: List[Tuple[str, float, Decimal, Decimal, date]] = []

    # ----- KPI helpers -----
    def _note_min_liquid(self, month_index: int):
        if self.liquid_bal < self.min_liquid:
            self.min_liquid = self.liquid_bal
            self.min_liquid_month = month_index

    def _note_peak_od(self, month_index: int):
        if self.od_bal > self.peak_od:
            self.peak_od = self.od_bal
            self.peak_od_month = month_index

    # ----- Month boundaries -----
    def begin_month(self, posting: date) -> Decimal:
        """
        Return OD interest accrued from last month to be treated as outflow THIS month (per audit fix).
        Resets the carried amount to zero.
        """
        due = self.od_interest_due_next_month
        self.od_interest_due_next_month = D(0)
        return due

    def end_month_accrue_od_interest(self):
        """
        Compute OD interest on current OD balance for this month and carry it to next month’s outflow bucket.
        """
        if self.od_bal <= 0:
            return
        od_rate_m = pct_to_decimal(self.ctx.portfolio.funding_strategy.od_annual_rate_pct) / D(12)
        interest = self.od_bal * od_rate_m
        if interest > 0:
            self.od_interest_due_next_month += interest

    # ----- Surplus handler -----
    def add_surplus(self, month_index: int, amount: Decimal):
        self.liquid_bal += amount
        # min_liquid only decreases, so we don't need to change it when surplus arrives

    # ----- Waterfall -----
    def cover_shortfall(
        self,
        month_index: int,
        posting: date,
        shortfall: Decimal,
        ledger: Ledger,
        bank_paid_so_far: Dict[str, Decimal],
        outstanding: Dict[str, Decimal],
        properties: List[Property],
    ):
        """
        Execute funding stack in order until shortfall is covered or sources exhausted.
        Writes ledger lines for each funding action.
        Updates self.liquid_bal, self.od_bal, outstanding[], bank_paid_so_far[] as needed.
        """
        need = Decimal(shortfall)

        # 1) Liquid
        use_liq = min(self.liquid_bal, need)
        if use_liq > 0:
            self.liquid_bal -= use_liq
            ledger.lines.append(LedgerLine(
                date=posting, label="Funding from Liquid", category="funding_liquid",
                amount=Money(use_liq), reason="Cover shortfall from cash"
            ))
            need -= use_liq
            self._note_min_liquid(month_index)

        if need <= 0:
            return

        # 2) MF FIFO (gross proceeds minus exit load; CGT handled at FY by TaxEngine)
        if self.ctx.portfolio.funding_strategy.mf_fifo_enabled and need > 0 and self.mf_fifo:
            need = self._redeem_mf_fifo(month_index, posting, need, ledger)

        if need <= 0:
            return

        # 3) OD draw (respect limit; 0 means NO OD)
        if need > 0:
            od_limit = D(self.ctx.portfolio.funding_strategy.od_limit or 0)
            if od_limit > 0:
                headroom = max(D(0), od_limit - self.od_bal)
                draw = min(headroom, need)
                if draw > 0:
                    self.od_bal += draw
                    ledger.lines.append(LedgerLine(
                        date=posting, label="OD Draw", category="funding_od",
                        amount=Money(draw), reason="Cover residual shortfall via OD"
                    ))
                    need -= draw
                    self._note_peak_od(month_index)

        if need <= 0:
            return

        # 4) HL Top-up (conservative headroom approach)
        for prop in properties:
            if prop.loan is None:
                continue
            from .loan_engine import LoanEngine
            le = LoanEngine()
            max_loan = le.compute_max_loan_cap(prop)
            headroom = max(Decimal("0"), max_loan - bank_paid_so_far.get(prop.id, D(0)))
            if headroom <= 0:
                continue

            topup = min(headroom, need)
            if topup > 0:
                # Treat as new disbursal increasing outstanding and bank_paid_so_far
                outstanding[prop.id] = outstanding.get(prop.id, D(0)) + topup
                bank_paid_so_far[prop.id] = bank_paid_so_far.get(prop.id, D(0)) + topup
                ledger.lines.append(LedgerLine(
                    date=posting, label="HL Top-up", category="funding_hl_topup",
                    amount=Money(topup), property_id=prop.id,
                    reason="Top-up within LTV headroom to cover shortfall"
                ))
                # Explicit risk flag phrasing (required by tests/UX)
                try:
                    self.ctx.timeline.risk_flags.append(
                        f"Relied on HL Top-up of ₹{float(topup):,.0f} — non-guaranteed source; bank approval required."
                    )
                except Exception:
                    pass

                need -= topup
                if need <= 0:
                    break
        # If still positive 'need', sources exhausted; deficit will reflect in this month's net cash
        return

    # ----- MF FIFO redemption -----
    def _redeem_mf_fifo(self, month_index: int, posting: date, need: Decimal, ledger: Ledger) -> Decimal:
        """
        Sell MF units FIFO until 'need' is covered by NET proceeds (after exit load).
        Records redemption logs for TaxEngine later.
        """
        remaining = need
        new_fifo: List[MFHoldingLot] = []
        for lot in self.mf_fifo:
            if remaining <= 0:
                new_fifo.append(lot)
                continue
            per_unit_net = Decimal(str(lot.current_nav)) * (Decimal(1) - pct_to_decimal(lot.exit_load_pct))
            if per_unit_net <= 0:
                new_fifo.append(lot)
                continue

            units_needed = (remaining / per_unit_net).quantize(Decimal("0.0001"))
            units_to_sell = min(Decimal(str(lot.units)), units_needed)
            if units_to_sell <= 0:
                new_fifo.append(lot)
                continue

            gross = Decimal(str(lot.current_nav)) * units_to_sell
            exit_load = gross * pct_to_decimal(lot.exit_load_pct)
            net = gross - exit_load

            ledger.lines.append(LedgerLine(
                date=posting, label=f"MF Redemption - {lot.scheme_name}", category="funding_mf",
                amount=Money(net), reason="FIFO redemption to fund shortfall",
                formula_hint="units*nav - exit_load"
            ))
            self.mf_redemptions_log.append((lot.id, float(units_to_sell), gross, exit_load, posting))

            # Track MF usage for risk narrative
            try:
                ctx = self.ctx
                if not hasattr(ctx, "_mf_usage_total"):
                    setattr(ctx, "_mf_usage_total", D(0))
                ctx._mf_usage_total = getattr(ctx, "_mf_usage_total") + net
            except Exception:
                pass

            # Update lot
            lot_units_left = Decimal(str(lot.units)) - units_to_sell
            if lot_units_left > 0:
                lot_copy = MFHoldingLot(**dict(lot))
                lot_copy.units = float(lot_units_left)
                new_fifo.append(lot_copy)

            remaining -= net

        self.mf_fifo = new_fifo
        return remaining
