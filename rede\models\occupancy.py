from pydantic import BaseModel, Field, root_validator
from ..types import Money, Percent, D

class OwnershipCosts(BaseModel):
    # Optional: either monthly_maintenance_per_sqft * area OR maintenance_monthly directly
    area_sqft: float = 0.0
    monthly_maintenance_per_sqft: Money = D(0)
    maintenance_monthly: Money = D(0)  # convenience field used in tests
    annual_maintenance_escalation_pct: Percent = D(7)
    # accept older name via pre-alias
    maintenance_escalation_pct: Percent | None = None

    annual_property_tax: Money = D(0)
    annual_insurance: Money = D(0)
    insurance_escalation_pct: Percent = D(5)
    annual_ground_rent: Money | None = None  # if leasehold, else None

    @root_validator(pre=True)
    def _alias_maint_escalation(cls, values):
        if "maintenance_escalation_pct" in values and "annual_maintenance_escalation_pct" not in values:
            values["annual_maintenance_escalation_pct"] = values["maintenance_escalation_pct"]
        return values

class OccupancyPlan(BaseModel):
    # Self-occupation
    move_in_month: int | None = None
    continue_renting_elsewhere: bool = False

    # Waiting period rent (used heavily in tests)
    rent_paid_while_waiting: Money = D(0)
    rent_escalation_annual_pct: Percent = D(5)
    current_rent_per_month: Money = D(0)  # kept for compatibility

    # Investment (let-out)
    let_out_month: int | None = None
    link_rent_yield_to_value: bool = True
    rent_yield_pct_of_value: Percent = D(3)
    annual_rent_escalation_pct: Percent = D(5)
    vacancy_pct: Percent = D(5)
    collection_efficiency_pct: Percent = D(98)

    # Fees/Deposits
    letting_brokerage_months: float = 1.0
    property_manager_fee_pct_of_rent: Percent = D(0)  # percentage model
    pm_fee_monthly: Money = D(0)                      # absolute model used in tests
    security_deposit_months: float = 0.0
    deposit_months: float = 0.0  # accepted alias in tests

    rent_control_risk_toggle: bool = False

    @root_validator(pre=True)
    def _alias_deposit(cls, values):
        # accept 'deposit_months' or 'security_deposit_months' interchangeably
        if "deposit_months" in values and "security_deposit_months" not in values:
            values["security_deposit_months"] = values["deposit_months"]
        return values
