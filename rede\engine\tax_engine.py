from __future__ import annotations
from dataclasses import dataclass
from decimal import Decimal
from datetime import date
from typing import Dict, List, Tuple, Optional
from ..types import Money, D
from ..engine.ledger import Ledger, LedgerLine
from ..engine.context import EngineContext
from ..models.property import Property
from ..models.core import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Person
from ..utils.money import pct_to_decimal

# -----------------------------
# Helpers
# -----------------------------

def fy_label(d: date) -> str:
    """Indian FY label. FY starts on Apr 1 and ends Mar 31."""
    if d.month >= 4:
        y1 = d.year
        y2 = d.year + 1
    else:
        y1 = d.year - 1
        y2 = d.year
    return f"FY{y1}-{str(y2)[-2:]}"

def last_day_of_fy_label(fy: str) -> date:
    """
    'FY2025-26' -> March 31, 2026
    """
    y2 = int(fy.split("-")[1])  # '26'
    y1 = int(fy[2:6])           # 2025
    full_y2 = (y1 // 100) * 100 + y2  # 2026
    return date(full_y2, 3, 31)

def days_between(a: date, b: date) -> int:
    return (b - a).days

# -----------------------------
# Core Engine
# -----------------------------

@dataclass
class PreconstructionBucket:
    total_interest: Decimal = D(0)
    # Once OC is achieved, this bucket is locked and spread 1/5 over next five FYs:
    start_fy: Optional[str] = None  # first FY when 1/5 eligible
    remaining_slices: int = 5

class TaxEngine:
    """
    Handles:
      - IHP: pre-construction interest (1/5), post-OC monthly interest split by occupancy (self-occupied vs let-out)
      - Section 24(b) caps & carry-forward (HP loss)
      - Section 80C: principal (post-OC), per-person cap
      - Rent TDS 194-IB: credits (info panel)
      - MF CGT: use FundingEngine FIFO logs; compute tax by FY; add ledger outflow at FY end
      - Purchase TDS 194-IA is modeled in Timeline; no buyer credit
    """

    def __init__(self, ctx: EngineContext, portfolio: Portfolio):
        self.ctx = ctx
        self.portfolio = portfolio

        # Accumulators keyed by FY label
        self.ihp_interest_selfocc_by_fy: Dict[str, Decimal] = {}
        self.ihp_interest_letout_by_fy_prop: Dict[str, Dict[str, Decimal]] = {}
        self.ihp_loss_carry_forward_by_person: Dict[str, List[Tuple[str, Decimal]]] = {}
        self.precon_by_prop: Dict[str, PreconstructionBucket] = {}

        self.principal_80c_by_fy_person: Dict[str, Dict[str, Decimal]] = {}
        self.rent_effective_by_fy_prop: Dict[str, Dict[str, Decimal]] = {}
        self.municipal_tax_by_fy_prop: Dict[str, Dict[str, Decimal]] = {}

        self.rent_tds_credit_by_fy: Dict[str, Decimal] = {}

        # MF redemptions: (lot_id, units_sold, gross, exit_load, sale_date)
        self.mf_redemptions: List[Tuple[str, float, Decimal, Decimal, date]] = []

        # Cached owner shares per property: property_id -> {person_id: share(0..1)}
        self.owner_shares: Dict[str, Dict[str, Decimal]] = self._compute_owner_shares()

        # Config hooks
        self.cfg_ihp = (self.ctx.tax_laws_fy.content.get("ihp", {}) if self.ctx.tax_laws_fy else {}) or {
            "self_occupied_interest_cap_per_person": 200000,
            "hp_loss_setoff_cap_per_person": 200000,
            "carry_forward_years": 8
        }
        self.cfg_80c = (self.ctx.tax_laws_fy.content.get("section80c", {}) if self.ctx.tax_laws_fy else {}) or {
            "cap_per_person": 150000
        }
        self.cfg_mf = (self.ctx.tax_laws_fy.content.get("mf_tax", {}) if self.ctx.tax_laws_fy else {}) or {
            "equity": {
                "holding_days_ltcg": 365,
                "stcg_rate_pct": 15.0,
                "ltcg_rate_pct": 10.0,
                "ltcg_annual_exemption": 100000.0,
                "indexation_allowed": False
            },
            "debt": {
                "holding_days_ltcg": 1095,
                "stcg_rate_pct": 30.0,
                "ltcg_rate_pct": 20.0,
                "ltcg_annual_exemption": 0.0,
                "indexation_allowed": False
            }
        }
        self.cfg_prop_cgt = (self.ctx.tax_laws_fy.content.get("property_cgt", {}) if self.ctx.tax_laws_fy else {}) or {
            "stcg_rate_pct": 30.0,
            "ltcg_rate_pct": 20.0,
            "holding_days_ltcg": 730
        }

        self._deferred_property_cgt: Dict[str, Decimal] = {}

    # -----------------------------
    # Ownership shares
    # -----------------------------
    def _compute_owner_shares(self) -> Dict[str, Dict[str, Decimal]]:
        shares: Dict[str, Dict[str, Decimal]] = {}
        persons: List[Person] = self.portfolio.persons
        for prop in self.portfolio.properties:
            s: Dict[str, Decimal] = {}
            total = D(0)
            for person in persons:
                pct = D(person.shareholdings.get(prop.id, 0))
                if pct > 0:
                    s[person.id] = pct / D(100)
                    total += pct
            if total == 0 and persons:
                s[persons[0].id] = D(1)
            elif total != D(100) and total > 0:
                for pid in s:
                    s[pid] = s[pid] / (total / D(100))
            shares[prop.id] = s
        return shares

    # -----------------------------
    # Monthly accruals
    # -----------------------------
    def accrue_interest(self, prop: Property, posting: date, amount: Decimal, *, is_preconstruction: bool, is_letout: bool):
        if amount <= 0:
            return
        fy = fy_label(posting)
        pid = prop.id

        if is_preconstruction:
            b = self.precon_by_prop.get(pid)
            if b is None:
                b = PreconstructionBucket()
                self.precon_by_prop[pid] = b
            b.total_interest += amount
            return

        if is_letout:
            self.ihp_interest_letout_by_fy_prop.setdefault(fy, {})
            self.ihp_interest_letout_by_fy_prop[fy].setdefault(pid, D(0))
            self.ihp_interest_letout_by_fy_prop[fy][pid] += amount
        else:
            self.ihp_interest_selfocc_by_fy.setdefault(fy, D(0))
            self.ihp_interest_selfocc_by_fy[fy] += amount

    def accrue_principal_80c(self, prop: Property, posting: date, amount: Decimal):
        if amount <= 0:
            return
        fy = fy_label(posting)
        for pid, sh in self.owner_shares.get(prop.id, {}).items():
            self.principal_80c_by_fy_person.setdefault(fy, {})
            self.principal_80c_by_fy_person[fy].setdefault(pid, D(0))
            self.principal_80c_by_fy_person[fy][pid] += amount * sh

    def accrue_rent_effective(self, prop: Property, posting: date, effective_rent: Decimal):
        if effective_rent <= 0:
            return
        fy = fy_label(posting)
        self.rent_effective_by_fy_prop.setdefault(fy, {})
        self.rent_effective_by_fy_prop[fy].setdefault(prop.id, D(0))
        self.rent_effective_by_fy_prop[fy][prop.id] += effective_rent

    def accrue_property_tax(self, prop: Property, posting: date, tax_amount: Decimal):
        if tax_amount <= 0:
            return
        fy = fy_label(posting)
        self.municipal_tax_by_fy_prop.setdefault(fy, {})
        self.municipal_tax_by_fy_prop[fy].setdefault(prop.id, D(0))
        self.municipal_tax_by_fy_prop[fy][prop.id] += tax_amount

    def accrue_rent_tds_credit(self, posting: date, tds_amount: Decimal):
        if tds_amount <= 0:
            return
        fy = fy_label(posting)
        self.rent_tds_credit_by_fy.setdefault(fy, D(0))
        self.rent_tds_credit_by_fy[fy] += tds_amount

    def set_preconstruction_start_on_oc(self, prop: Property, oc_date: date):
        b = self.precon_by_prop.get(prop.id)
        if b and b.start_fy is None:
            b.start_fy = fy_label(oc_date)
            b.remaining_slices = 5

    def set_mf_redemptions(self, items: List[Tuple[str, float, Decimal, Decimal, date]]):
        self.mf_redemptions.extend(items)

    # -----------------------------
    # FY settlement (merged version: MF CGT + deferred property CGT + panels)
    # -----------------------------
    def settle_fy(self, fy: str, ledger: Ledger):
        # ---- 1) Preconstruction 1/5 eligibility ----
        precon_slices_this_fy: Dict[str, Decimal] = {}
        for pid, bucket in self.precon_by_prop.items():
            if bucket.total_interest <= 0:
                continue
            if bucket.start_fy is None:
                continue
            start_y = int(bucket.start_fy[2:6])
            fy_y = int(fy[2:6])
            if 0 <= (fy_y - start_y) < 5 and bucket.remaining_slices > 0:
                slice_amt = bucket.total_interest / D(5)
                precon_slices_this_fy[pid] = slice_amt
                bucket.remaining_slices -= 1

        # ---- 2) Section 24(b) computation ----
        cap_24b = D(self.cfg_ihp.get("self_occupied_interest_cap_per_person", 200000))
        setoff_cap = D(self.cfg_ihp.get("hp_loss_setoff_cap_per_person", 200000))
        carry_years = int(self.cfg_ihp.get("carry_forward_years", 8))

        selfocc_interest = self.ihp_interest_selfocc_by_fy.get(fy, D(0))
        precon_total = sum(precon_slices_this_fy.values()) if precon_slices_this_fy else D(0)
        selfocc_interest += precon_total

        letout_per_prop = self.ihp_interest_letout_by_fy_prop.get(fy, {})
        rent_nav_per_prop = self.rent_effective_by_fy_prop.get(fy, {})
        municipal_per_prop = self.municipal_tax_by_fy_prop.get(fy, {})

        ihp_panel: Dict[str, Dict[str, Decimal]] = {}
        persons = self.portfolio.persons

        participating = [p.id for p in persons]
        per_person_selfocc = (selfocc_interest / D(len(participating))) if participating and selfocc_interest > 0 else D(0)

        for p in persons:
            ihp_panel.setdefault(p.id, {"selfocc_interest_allowed": D(0), "letout_net_hp": D(0), "hp_loss_carry_forward_added": D(0)})
            allow = min(cap_24b, per_person_selfocc)
            ihp_panel[p.id]["selfocc_interest_allowed"] = allow

        letout_net_hp_total = D(0)
        for prop in self.portfolio.properties:
            pid = prop.id
            rent_val = rent_nav_per_prop.get(pid, D(0))
            muni = municipal_per_prop.get(pid, D(0))
            std_ded = (rent_val - muni) * D("0.30") if (rent_val - muni) > 0 else D(0)
            nav = rent_val - muni - std_ded
            interest = letout_per_prop.get(pid, D(0))
            hp_income = nav - interest
            letout_net_hp_total += hp_income

        per_person_letout = (letout_net_hp_total / D(len(persons))) if persons else D(0)
        for p in persons:
            ihp_panel[p.id]["letout_net_hp"] = per_person_letout

        for p in persons:
            hp_total = ihp_panel[p.id]["selfocc_interest_allowed"] + ihp_panel[p.id]["letout_net_hp"]
            if hp_total < 0:
                setoff_used = max(-setoff_cap, hp_total)  # hp_total negative
                carry = hp_total - setoff_used
                if carry < 0 and carry_years > 0:
                    lst = self.ihp_loss_carry_forward_by_person.setdefault(p.id, [])
                    lst.append((fy, carry))
                    ihp_panel[p.id]["hp_loss_carry_forward_added"] = carry

        # ---- 3) Section 80C principal per person ----
        cap_80c = D(self.cfg_80c.get("cap_per_person", 150000))
        s80_panel: Dict[str, Decimal] = {}
        for p in persons:
            amt = self.principal_80c_by_fy_person.get(fy, {}).get(p.id, D(0))
            s80_panel[p.id] = min(cap_80c, amt) if amt > 0 else D(0)

        # ---- 4) MF CGT for this FY (cash outflow at FY end) ----
        tax_due_mf = D(0)
        if self.mf_redemptions:
            lots_by_id: Dict[str, MFHoldingLot] = {lot.id: lot for lot in self.portfolio.mf_holdings}
            for lot_id, units_sold, gross, exit_load, sale_dt in list(self.mf_redemptions):
                if fy_label(sale_dt) != fy:
                    continue
                lot = lots_by_id.get(lot_id)
                if not lot:
                    continue
                cost_per_unit = D(lot.nav_acquired)
                sale_per_unit = D(gross) / D(units_sold) if units_sold > 0 else D(0)
                gain = (sale_per_unit - cost_per_unit) * D(units_sold)

                acq_dt = date.fromisoformat(lot.acquired_date)
                hold_days = (sale_dt - acq_dt).days
                rules = self.cfg_mf.get(lot.asset_type, self.cfg_mf["equity"])
                thresh = int(rules.get("holding_days_ltcg", 365))

                if hold_days > thresh:
                    exmpt = D(rules.get("ltcg_annual_exemption", 0.0))
                    taxable = max(D(0), gain - exmpt)
                    rate = D(rules.get("ltcg_rate_pct", 10.0)) / D(100)
                    tax_due_mf += taxable * rate
                else:
                    rate = D(rules.get("stcg_rate_pct", 15.0)) / D(100)
                    tax_due_mf += max(D(0), gain) * rate

        # ---- 5) Deferred Property CGT for this FY ----
        tax_due_prop_deferred = self._deferred_property_cgt.get(fy, D(0))

        # Post a combined tax payment on the FY end date
        total_tax_due = tax_due_mf + tax_due_prop_deferred
        if total_tax_due > 0:
            pay_date = last_day_of_fy_label(fy)
            if tax_due_mf > 0:
                LedgerLine.update_forward_refs()
                ledger.lines.append(LedgerLine(
                    date=pay_date, label=f"MF CGT ({fy})", category="tax",
                    amount=Money(-tax_due_mf), reason="Capital gains tax on mutual fund redemptions"
                ))
            if tax_due_prop_deferred > 0:
                ledger.lines.append(LedgerLine(
                    date=pay_date, label=f"Property CGT ({fy})", category="tax",
                    amount=Money(-tax_due_prop_deferred), reason="Deferred property capital gains tax"
                ))

        # ---- 6) Panel (for API/UI) ----
        self._last_settle_panel = {
            "fy": fy,
            "ihp": {
                "selfocc_total_interest_allowed": float(sum(v["selfocc_interest_allowed"] for v in ihp_panel.values())),
                "letout_net_hp_total": float(letout_net_hp_total),
                "carry_forward_added_total": float(sum(v["hp_loss_carry_forward_added"] for v in ihp_panel.values())),
            },
            "sec80c": {p.id: float(s80_panel[p.id]) for p in persons},
            "rent_tds_credit": float(self.rent_tds_credit_by_fy.get(fy, D(0))),
            "mf_cgt_paid": float(tax_due_mf),
            "property_cgt_deferred_paid": float(tax_due_prop_deferred),
        }

    # -----------------------------
    # Property CGT
    # -----------------------------
    def _cii(self, fy_label_str: str) -> Decimal:
        cii_tab = (self.ctx.tax_laws_fy.content.get("cii", {}) if self.ctx.tax_laws_fy else {})
        val = cii_tab.get(fy_label_str)
        return D(val) if val is not None else D(1)

    def _fy_of_date(self, d: date) -> str:
        return fy_label(d)

    def compute_property_cgt(self, *, prop, sale_date: date, sale_gross: Decimal, transaction_costs: Decimal, acquisition_total: Decimal) -> Decimal:
        holding_thresh = int(self.cfg_prop_cgt.get("holding_days_ltcg", 730))
        stcg_rate = D(self.cfg_prop_cgt.get("stcg_rate_pct", 30.0)) / D(100)
        ltcg_rate = D(self.cfg_prop_cgt.get("ltcg_rate_pct", 20.0)) / D(100)

        acq_iso = getattr(prop.heads, "purchase_reference_date", None) or self.portfolio.start_date
        acq_dt = date.fromisoformat(acq_iso)
        hold_days = (sale_date - acq_dt).days

        consideration = max(D(0), D(sale_gross) - D(transaction_costs))

        if hold_days >= holding_thresh:
            acq_fy = self._fy_of_date(acq_dt)
            sale_fy = self._fy_of_date(sale_date)
            cii_acq = self._cii(acq_fy)
            cii_sale = self._cii(sale_fy)
            indexed_cost = acquisition_total if cii_acq <= 0 or cii_sale <= 0 else D(acquisition_total) * (cii_sale / cii_acq)
            gain = max(D(0), consideration - indexed_cost)
            return gain * ltcg_rate
        else:
            gain = max(D(0), consideration - D(acquisition_total))
            return gain * stcg_rate

    def estimate_property_cgt_now(self, *, prop, sale_gross: Decimal, transaction_costs: Decimal) -> Decimal:
        basis = getattr(prop, "_acq_total_cached", None)
        if basis is None:
            return D(0)
        today = date.today()
        return self.compute_property_cgt(prop=prop, sale_date=today,
                                         sale_gross=sale_gross, transaction_costs=transaction_costs,
                                         acquisition_total=D(basis))

    def defer_property_cgt(self, sale_date: date, amount: Decimal):
        fy = fy_label(sale_date)
        self._deferred_property_cgt[fy] = self._deferred_property_cgt.get(fy, D(0)) + amount

    def get_panel(self) -> Dict:
        return getattr(self, "_last_settle_panel", {})
