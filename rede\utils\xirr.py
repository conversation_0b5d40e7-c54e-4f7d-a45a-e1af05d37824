from datetime import date
from decimal import Decimal, getcontext
from typing import Iterable, Tuple

getcontext().prec = 28

def _npv(rate: Decimal, flows: Iterable[Tuple[date, Decimal]]) -> Decimal:
    """NPV for arbitrary dates; rate is annual decimal."""
    if rate == Decimal("-1"):
        # avoid div by zero in (1+rate)
        rate = Decimal("-0.999999999")
    one = Decimal(1)
    total = Decimal(0)
    dates = [d for d,_ in flows]
    t0 = min(dates)
    for d, amt in flows:
        days = Decimal((d - t0).days)
        total += amt / (one + rate) ** (days / Decimal(365))
    return total

def xirr(cashflows: Iterable[Tuple[date, Decimal]], tol=Decimal("1e-8"), maxiter=100) -> Decimal:
    """
    Newton with bisection fallback.
    Returns annualized rate as Decimal (e.g., 0.12 for 12%).
    """
    flows = list(cashflows)
    pos = any(a > 0 for _, a in flows)
    neg = any(a < 0 for _, a in flows)
    if not (pos and neg):
        raise ValueError("XIRR requires at least one positive and one negative cash flow")

    # Initial bracket [-0.9, 5.0] (i.e., -90% to +500%)
    low, high = Decimal("-0.90"), Decimal("5.00")
    f_low, f_high = _npv(low, flows), _npv(high, flows)
    # Expand if same sign
    it = 0
    while f_low * f_high > 0 and it < 25:
        high *= Decimal("1.5")
        f_high = _npv(high, flows)
        it += 1

    if f_low == 0: return low
    if f_high == 0: return high

    # Bisection
    for _ in range(maxiter):
        mid = (low + high) / 2
        f_mid = _npv(mid, flows)
        if abs(f_mid) < tol:
            return mid
        if f_low * f_mid < 0:
            high, f_high = mid, f_mid
        else:
            low, f_low = mid, f_mid
    return mid
