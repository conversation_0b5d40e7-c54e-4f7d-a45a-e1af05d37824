"""
Typed adapters and validators for external JSON configs used by the engine.

This package is optional at runtime (engine only relies on dicts via LoadedConfig.content),
but is useful for validating and documenting the expected shapes of:
- state_rules.json
- city_benchmarks.json
- tax_laws.json
- rate_index.json
- bank_policies.json
"""

from .loader import load_json_with_hash, load_optional
from .state_rules import StateRules, parse_state_rules
from .city_benchmarks import CityBenchmarks, parse_city_benchmarks
from .tax_laws import TaxLaws, parse_tax_laws
from .rate_index_path import RateIndexSeries, parse_rate_index
from .bank_policies import BankPolicies, parse_bank_policies

__all__ = [
    "load_json_with_hash", "load_optional",
    "StateRules", "parse_state_rules",
    "CityBenchmarks", "parse_city_benchmarks",
    "TaxLaws", "parse_tax_laws",
    "RateIndexSeries", "parse_rate_index",
    "BankPolicies", "parse_bank_policies",
]
