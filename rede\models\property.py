from pydantic import BaseModel, Field, validator
from ..types import Money, Percent, D
from .legal import LegalProfile
from .heads import PriceHeads
from .plan import PaymentPlan, StageCap, PaymentEvent, PaymentTrigger
from .price import PriceModel
from .loans import TermLoan, OverdraftLoan, LTVBasis
from .occupancy import OwnershipCosts, OccupancyPlan
from .exit import ExitSettings

# Acquisition basis fallback for resale / onboarded assets
class ExistingBasis(BaseModel):
    """
    Used if UC builder-event tracking is absent. Amounts assumed inclusive of GST at original purchase.
    """
    purchase_total: Money = D(0)                 # Agreement consideration actually paid to seller
    stamp_duty_paid: Money = D(0)
    registration_fee_paid: Money = D(0)
    other_acquisition_costs: Money = D(0)        # legal, society transfer (purchase-side), etc.
    improvements_capitalized: Money = D(0)       # interiors/improvements as capital improvements

class Property(BaseModel):
    id: str
    city: str
    name: str | None = None
    state: str | None = None

    # Heads & legal
    legal: LegalProfile = Field(default_factory=LegalProfile)
    heads: PriceHeads

    # Which heads appreciate; default empty (engine can derive sensible default if needed)
    appreciation_base: set[str] = Field(default_factory=set)

    # Payment plan & pricing model
    plan: PaymentPlan = Field(default_factory=PaymentPlan)
    price_model: PriceModel = Field(default_factory=lambda: PriceModel(base_annual_cagr_pct=D(0)))

    # Loans
    loan: TermLoan | OverdraftLoan | None = None
    bank_stage_caps: list[StageCap] | None = None
    ltv_basis: LTVBasis | None = None

    # Costs/Occupancy
    ownership_costs: OwnershipCosts = Field(default_factory=OwnershipCosts)
    occupancy: OccupancyPlan = Field(default_factory=OccupancyPlan)

    # Resale/onboarded basis, and exit config
    existing_basis: ExistingBasis | None = None
    exit: ExitSettings = Field(default_factory=ExitSettings)

    @validator("appreciation_base")
    def valid_heads(cls, v):
        allowed = {
            "base_price_ex_gst", "plc_floor_rise", "parking_charges",
            "club_membership", "other_capital_heads"
        }
        if not v.issubset(allowed):
            raise ValueError(f"appreciation_base must be subset of {allowed}")
        return v

# Re-exports so tests can import all from rede.models.property
PropertyHeads = PriceHeads
PropertyOwnershipCosts = OwnershipCosts
PropertyOccupancy = OccupancyPlan
