import datetime as dt
from decimal import Decimal as D

from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Port<PERSON>lio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PaymentEvent, PaymentTrigger, PropertyOwnershipCosts, PropertyOccupancy
from rede.models.enums import PaymentTriggerType
from rede.types import D as DD

def _mini_ctx(months=2):
    # Minimal configs; only what we touch
    tax_laws = {
        "tds_rules": {"rent_194IB": {"monthly_threshold": 50000, "rate_pct": 5}},
        "mf_tax": {"equity": {"holding_days_ltcg": 365, "stcg_rate_pct": 15, "ltcg_rate_pct": 10, "ltcg_annual_exemption": 100000, "indexation_allowed": False}},
        "property_cgt": {"stcg_rate_pct": 30, "ltcg_rate_pct": 20, "holding_days_ltcg": 730},
        "cii": {"FY2024-25": 348, "FY2025-26": 360}
    }
    class _Cfg:
        content = tax_laws
    # EngineContext expects .tax_laws_fy with .content
    ctx = EngineContext()
    ctx.tax_laws_fy = _Cfg()
    return ctx

def _portfolio_with_single_outflow(od_rate_pct=12):
    p = Person(id="p1", name="Owner", tax_regime="old", shareholdings={}, liquid_cash=DD(0))
    # One event at t=0 demanding ₹100, not financeable by bank -> outflow -> triggers OD
    ev = PaymentEvent(
        name="Test Demand",
        stage="Booking",
        trigger=PaymentTrigger(type=PaymentTriggerType.month, month=0),
        percent_of_base=DD(100),
        gst_pct=DD(0),
        financeable_by_bank=False
    )
    prop = Property(
        id="prop1",
        city="Bengaluru",
        heads=PropertyHeads(base_price_ex_gst=DD(100)),  # ₹100 base -> full outflow
        plan=PaymentPlan(events=[ev]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(),
        loan=None
    )
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=1,
        persons=[p], properties=[prop],
        mf_holdings=[],
        user_starting_liquid_cash_by_person={"p1": DD(0)},  # no cash
        alt_invest_return_after_tax_pct=DD(6),
        loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(10_000_000), od_annual_rate_pct=DD(od_rate_pct), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(),
        configs=RuntimeConfigs("","","","","")
    )
    return pf

def test_od_interest_defers_one_month():
    ctx = _mini_ctx()
    pf = _portfolio_with_single_outflow(od_rate_pct=12)
    eng = TimelineEngine(ctx)
    eng.ctx.portfolio = pf
    eng.run()
    led = eng.result()["ledger"]

    # Month 0: expect OD Draw (to fund ₹100), but NO "OD Interest (due)"
    m0 = [l for l in led if l["date"].endswith("-04-05")]  # start_date posting day
    assert any("OD Draw" in l["label"] for l in m0), "Expected OD Draw in month 0"
    assert not any("OD Interest (due)" in l["label"] for l in m0), "OD interest should NOT be posted in month 0"

    # Month 1: interest for month 0 should be due now
    m1 = [l for l in led if l["date"].endswith("-05-05")]
    od_interest_lines = [l for l in m1 if "OD Interest (due)" in l["label"]]
    assert od_interest_lines, "Expected OD Interest (due) posted in month 1"

    # Check amount: interest = 100 * (12%/12) = 1.0
    amt = sum(D(str(l["amount"])) for l in od_interest_lines)  # amounts are negative outflows
    assert amt == D("-1"), f"Expected OD interest of -1.0, got {amt}"
