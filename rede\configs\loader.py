from __future__ import annotations
import json, hashlib
from pathlib import Path
from typing import Optional, Dict, Any

# Use the single canonical LoadedConfig used across engine/CLI/tests
from ..engine.config_loader import LoadedConfig

def _compute_sha256_bytes(raw: bytes) -> str:
    h = hashlib.sha256()
    h.update(raw)
    return h.hexdigest()

def load_json_with_hash(path: str) -> LoadedConfig:
    """
    Strict loader: raises if file missing. Use load_optional() for tolerant behavior.
    Returns LoadedConfig(path=..., content=dict, sha256=...).
    """
    p = Path(path)
    raw = p.read_bytes()
    return LoadedConfig(path=str(p), content=json.loads(raw), sha256=_compute_sha256_bytes(raw))

def load_optional(path: Optional[str]) -> LoadedConfig:
    """
    Tolerant loader: if path is falsy or file is missing, returns empty content and blank hash.
    This keeps the EngineContext happy without special casing.
    """
    if not path:
        return LoadedConfig(path="", content={}, sha256="")
    p = Path(path)
    if not p.exists():
        return LoadedConfig(path=str(p), content={}, sha256="")
    raw = p.read_bytes()
    return LoadedConfig(path=str(p), content=json.loads(raw), sha256=_compute_sha256_bytes(raw))
