from decimal import Decimal as D
from datetime import date
from rede.engine.tax_engine import TaxEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy

def _ctx_pf():
    class _Cfg: content = {
        "ihp": {"self_occupied_interest_cap_per_person": 200000, "hp_loss_setoff_cap_per_person": 200000, "carry_forward_years": 8},
        "section80c": {"cap_per_person": 150000}
    }
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    p = Person(id="p1", name="A", tax_regime="old", shareholdings={}, liquid_cash=D(0))
    prop = Property(id="prop1", city="BLR",
                    heads=PropertyHeads(base_price_ex_gst=D(0)),
                    plan=PaymentPlan(events=[]),
                    ownership_costs=PropertyOwnershipCosts(),
                    occupancy=PropertyOccupancy())
    pf = Portfolio(currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=12,
                   persons=[p], properties=[prop], mf_holdings=[],
                   user_starting_liquid_cash_by_person={"p1": D(0)},
                   alt_invest_return_after_tax_pct=D(6), loan_sanction_risk_pct=D(0),
                   funding_strategy=FundingStrategy(), tax_settings_global=TaxSettingsGlobal(),
                   configs=RuntimeConfigs("","","","",""))
    return ctx, pf, prop

def test_self_occupied_interest_cap_applied():
    ctx, pf, prop = _ctx_pf()
    tax = TaxEngine(ctx, pf)
    fy = "FY2025-26"
    # Post-OC interest (self-occupied) totaling ₹300,000 in FY -> cap per person = ₹200,000
    for m in range(12):
        tax.accrue_interest(prop, date(2025, 4, 5), D(25000), is_preconstruction=False, is_letout=False)
    tax.settle_fy(fy, ledger=type("L", (), {"lines": []})())
    panel = tax.get_panel()
    assert abs(panel["ihp"]["selfocc_total_interest_allowed"] - 200000.0) < 1e-6
