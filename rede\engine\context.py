from __future__ import annotations
from pydantic import BaseModel, Field
from datetime import date
from typing import Dict, Optional
from ..types import Money
from ..models.core import Portfolio
from .config_loader import LoadedConfig

class ConfigHashes(BaseModel):
    state_rules_sha256: str = ""
    city_benchmarks_sha256: str = ""
    tax_laws_fy_sha256: str = ""
    rate_index_path_sha256: str = ""
    bank_policies_sha256: str = ""

class EngineContext(BaseModel):
    """
    Shared runtime context; all config objects expose `.content` dicts.
    All fields are optional to allow two-stage wiring in tests/CLI:
      ctx = EngineContext(); ctx.tax_laws_fy = LoadedConfig(None, {...}, None)
    """
    portfolio: Optional[Portfolio] = None

    state_rules: Optional[LoadedConfig] = None
    city_benchmarks: Optional[LoadedConfig] = None
    tax_laws_fy: Optional[LoadedConfig] = None
    rate_index_path: Optional[LoadedConfig] = None
    bank_policies: Optional[LoadedConfig] = None

    config_hashes: ConfigHashes = Field(default_factory=ConfigHashes)

class MonthState(BaseModel):
    month_index: int
    date_posting: date

    property_values: Dict[str, Money] = Field(default_factory=dict)
    loan_balances: Dict[str, Money] = Field(default_factory=dict)
    liquid_balances_by_person: Dict[str, Money] = Field(default_factory=dict)
    od_balance: Optional[Money] = None
    tds_credits_by_person: Dict[str, Money] = Field(default_factory=dict)
