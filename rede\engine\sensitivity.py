from ..utils.jsonpath_patch import apply_patches
from ..api.contracts import SensitivityRunnerSpec

class SensitivityRunner:
    """
    Implements Add-On A per schema.
    """

    def run(self, base_portfolio, spec: SensitivityRunnerSpec):
        """
        - Run base
        - For each one_way_sensitivity: apply deltas via JSONPath, rerun, collect KPI deltas
        - For scenario grid/full factorial: patch, run, collect KPIs
        """
        raise NotImplementedError
