from datetime import date
from calendar import monthrange

def _last_day_of_month(y: int, m: int) -> int:
    return monthrange(y, m)[1]

def add_months(d: date, months: int) -> date:
    """
    Add 'months' to date 'd', clamping the day to the last valid day of the target month.
    Fixes end-of-month issues (e.g., Jan 31 + 1 mo -> Feb 29/28).
    """
    y_off, m0 = divmod(d.month - 1 + months, 12)
    y_new, m_new = d.year + y_off, m0 + 1
    day = min(d.day, _last_day_of_month(y_new, m_new))
    return d.replace(year=y_new, month=m_new, day=day)

def month_index(start_date: date, current: date) -> int:
    """
    Month index (0-based) ignoring days (timeline posts on a fixed day-of-month anyway).
    """
    return (current.year - start_date.year) * 12 + (current.month - start_date.month)
