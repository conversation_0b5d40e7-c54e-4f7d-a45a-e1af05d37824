Real Estate Decision Engine (REDE)
Master Functional & Technical Specification — v1.4 (India-focused, Standalone)

Purpose: A transparent, portfolio-capable decision engine for Indian residential real estate choices (RTM, UC, resale, upgrade, investor strategies). Produces cash-accurate monthly ledgers, tax-aware outcomes, net values (after costs & tax), and comparable return metrics (XIRR), with liquidity stress and funding resolution.

Audience: Product managers, engineers, QA, and advanced users. Labels and outputs must remain understandable to non-finance users.

0) Design Principles & Global Conventions

Cash-first: Everything reduces to a dated, monthly Net Cash Flow (NCF) ledger; all KPIs derive from it.

Explainability: Every ledger line carries a human reason and a formula_hint.

Config-driven: State/FY rules (stamp duty, CII, tax slabs, rent TDS, RBI tiers) live in external config files; never hard-code laws.

Portfolio-first: Multiple properties, co-owners/co-borrowers, multiple loans and exits coexist.

No black boxes: Assumptions and toggles are visible (e.g., indexation, interiors in value, CGT in MTM, loan prepayment penalties).

Simple → Advanced: Default to “Simple Mode”, expose full controls in “Detailed Mode.”

Units & ranges: Percent inputs are in % units (0–100); rupees are numeric; dates are ISO (YYYY-MM-DD).

India FY: Annual tax settlement uses India FY (Apr–Mar).

Posting day: All monthly postings occur on posting_day_of_month (default 5).

Legal note: Outputs guide planning; they are not legal/tax advice.

1) Core Objectives

Compare RTM, UC, upgrade, sell-1-buy-2, keep+buy, investor stage exits fairly.

Show Total Outflows/Inflows/Net, Realized XIRR, MTM XIRR, Net Value today, Liquidity stress (min balance, required buffer, OD use), Tax impacts (IHP, CGT, TDS).

Model payment plans (slab/calendar/subvention), bank disbursal caps, pre-EMI/EMI, floating rates, prepayment, rent saved/received, vacancy/collection, price shocks, legal gates (OC, Khata), buyer late-payment penalties, leasehold, MRTA, sanction risk.

Provide funding resolution: pre-fund buffer, planned top-ups, and auto funding stack (Liquid → MF FIFO → OD → HL top-up).

Offer Sensitivity Runner and CA Export packs.

1.1) Core Architectural Pattern — Property State Machine

A property follows a state machine:

PRE_BOOKING → UNDER_CONSTRUCTION → AWAITING_OC → SELF_OCCUPIED | RENTED_OUT → (optionally) SOLD

Transitions: booking, stage completions, possession (physical), oc_received (legal), move_in, let_out, sale.

Gates: OC is required for SELF_OCCUPIED or RENTED_OUT (if gating toggle ON).

State-linked costs:

Maintenance starts post-handover (society rules vary; configurable).

Rent paid while waiting; saved after move-in; received when let-out.

Pre-EMI until EMI start; ownership costs escalate annually.

2) Timeline, Notation, and KPIs

Time index 
𝑡
=
0
,
1
,
…
,
𝐻
t=0,1,…,H months from start_date.

NCF
𝑡
NCF
t
	​

: net cash flow at month 
𝑡
t (inflow +; outflow −).

𝐿
𝑡
L
t
	​

: liquid cash balance end of month 
𝑡
t.

𝐷
𝑡
D
t
	​

: outstanding debt end of month 
𝑡
t (per loan).

𝑉
𝑡
V
t
	​

: market value of property at 
𝑡
t.

𝑉
^
𝑇
V
T
	​

: MTM net sale at eval month 
𝑇
T after brokerage, transfer, debt closure/penalties, and CGT (togglable).

Realized XIRR: uses only realized cash flows to date.

MTM XIRR: adds 
+
𝑉
^
𝑇
+
V
T
	​

 on eval date 
𝑇
T.

3) Data Model (Portfolio-Ready)
3.1 Portfolio

currency: "INR"

start_date: date, posting_day_of_month: int (1–28, def 5)

horizon_months: int (≥24)

persons: Person[]

properties: Property[]

user_starting_liquid_cash_by_person: {person_id: ₹} (pooled if absent)

alt_invest_return_after_tax_pct: float (def 6%)

funding_strategy: FundingStrategy (§7)

tax_settings_global: TaxSettingsGlobal (§8)

configs: RuntimeConfigs (§14)

3.2 Person (co-owner/co-borrower capable)

id, name

tax_regime: "old"|"new"

income_profile? (needed for HRA)

shareholdings: {property_id: share_pct} (sum to 100%)

liquid_cash? (if separate pools are enabled)

3.3 Property

id, name, city, state

legal: LegalProfile

heads: PriceHeads

appreciation_base: set[str] (default excludes interiors)

plan: PaymentPlan

price_model: PriceModel

loan: TermLoan | OverdraftLoan | null

bank_stage_caps: StageCap[] | null

ltv_basis: LTVBasis

ownership_costs: OwnershipCosts

occupancy: OccupancyPlan

exit: ExitPlan

3.4 LegalProfile (due-diligence & risk)

khata_type: "A-Khata"|"B-Khata"|"NA" (def "A-Khata")

oc_status: "NotApplied"|"Applied"|"Granted" (RTM def "Granted")

cc_status: "NA"|"InProgress"|"Granted"

land_tenure: "Freehold"|"Leasehold" (+ annual_ground_rent, lease_years_remaining if leasehold)

encumbrance_flag: bool

b_khata_discount_pct: float (def 0, suggest 2–5)

oc_risk_discount_pct: float (def 0 unless OC pending, suggest 1–3)

bba_buyer_late_penalty_interest_pct_annual: float (def 18%)

buyer_late_penalty_grace_days: int (def 7)

Effects:

If B-Khata → ROI spread (+1–2% configurable) or NBFC mode warning.

Apply valuation discounts to 
𝑉
𝑡
V
t
	​

 if B-Khata/OC pending (toggle).

Late payment penalty accrues on overdue builder demands beyond grace.

3.5 PriceHeads (₹; defaults 0)

base_price_ex_gst (BSP)

plc_floor_rise, parking_charges, club_membership, other_capital_heads

gst_pct (use GST config presets per project type; parking/amenities may have 18% via overrides)

stamp_duty_pct (state rules)

registration_fixed | registration_pct (state rules)

brokerage_buy_pct, tds_buy_pct (194-IA; def 1.0)

khata_mutation, utility_connection, corpus_or_advance_maint

fitout_deposit_refundable, fitout_nonrefundable

interiors_capex, interiors_depr_years (def 10), interiors_residual_pct (def 20)

Leasehold: annual_ground_rent

3.6 PaymentPlan (event-based)

Each PaymentEvent:

name

trigger: {month_offset|stage|possession|handover|oc_received|custom_date}

base: "base_price_ex_gst"|"agreement_ex_gst"|"all_in_ex_stamp"

amount_type: "percent"|"fixed"; amount_value

gst_applicable: bool

financeable_by_bank: bool

subvention?: {payer:"builder", coverage:"pre_emi"|"emi", end_trigger}

due_by_day: int (def posting day)

Penalty link: if unresolved shortfall at due date → apply buyer late penalty monthly.

3.7 PriceModel

base_annual_cagr_pct

shocks: [{start_month, end_month, cumulative_pct}]

age_factor_curve?: [{age_year, discount_pct}] (optional age discounts)

interiors_in_value: bool (def false; toggle)

interiors_depr_years: 10, interiors_residual_pct: 20 (straight-line; non-cash)

Valuation formula:

𝑉
𝑡
=
(
𝐴
⋅
∏
𝑘
=
1
𝑡
(
1
+
𝑔
+
𝛿
𝑘
)
)
⋅
(
1
−
age_disc
𝑡
)
⋅
(
1
−
khata/oc_disc
)
V
t
	​

=(A⋅
k=1
∏
t
	​

(1+g+δ
k
	​

))⋅(1−age_disc
t
	​

)⋅(1−khata/oc_disc)

where 
𝐴
A = sum of appreciating heads (interiors excluded by default).

3.8 Loans & Sanction
3.8.1 Common

Sanction realism:

expected_sanctioned_amount

final_sanctioned_amount (if unknown, engine can compute stress case using loan_sanction_risk_pct at portfolio level; default 10%)

sanction_validity_months (def 4–6; warn if plan crosses validity)

LTV basis: exclude_gst_from_base: bool (def true); ltv_pct (capped by RBI tiers via configs).

RBI LTV tiers: configured by ticket size; engine caps effective ltv_pct.

Bank stage caps: StageCap{stage_name, max_pct_of_agreement_ex_gst}.

3.8.2 TermLoan

tenure_months, initial_roi_annual_pct

rate_index: "REPO"|"MCLR"|"FIXED", spread_pct

reset_frequency_months (def 3)

rate_shocks: [{month, new_index_pct}] (or read from rate_index_path.json)

on_rate_change_policy: "AdjustTenure" (def) | "AdjustEMI"

pre_emi_till_possession: bool, emi_start_month?: int

processing_fee_pct, processing_fee_gst_pct, valuation_legal_charges, mod_franking_pct

prepayment_policy: PrepaymentPolicy (tenure reduction def)

prepayment_penalty_pct_if_applicable (fixed-rate often >0; floating often 0; from bank config)

balance_transfer_events?: [{month, new_roi_pct, fee_pct, fee_fixed}]

topup_events?: [{month, amount, roi_pct}]

loan_insurance? (MRTA): {premium_payment:"UpfrontBundled"|"Annual", upfront_premium, annual_premium}

UpfrontBundled: increases principal before EMI calc

Annual: recurring premium outflow

3.8.3 OverdraftLoan (MaxGain-like)

All TermLoan attributes plus:

od_sweep_enabled: bool (def true)

Interest base:

interest
𝑡
=
(
principal
𝑡
−
od_account_bal
𝑡
)
×
roi
𝑡
12
interest
t
	​

=(principal
t
	​

−od_account_bal
t
	​

)×
12
roi
t
	​

	​


Surplus policy: if 
(
roi
𝑡
−
alt_return
)
≥
threshold
(roi
t
	​

−alt_return)≥threshold → sweep surplus to OD; else invest.

3.9 OwnershipCosts

monthly_maintenance_per_sqft, annual_maintenance_escalation_pct (def 7%)

annual_property_tax (+ escalation optional)

annual_insurance, insurance_escalation_pct (def 5%)

annual_ground_rent if leasehold

3.10 Occupancy & Rent

move_in_month?: int (must be ≥ oc_received_month if gating ON)

continue_renting_elsewhere: bool

current_rent_per_month (+ current_rent_escalation_pct)

Let-out model:

link_rent_yield_to_value: bool (def true)

rent_yield_pct_of_value (def 3%)

annual_rent_escalation_pct (def 5%), vacancy_pct (def 5%), collection_efficiency_pct (def 98%)

letting_brokerage_months (def 1), property_manager_fee_pct_of_rent (def 0–10; default 8 if PM used)

security_deposit_months (inflow at start of tenancy, outflow at end; optional breakage factor)

rent_control_risk_toggle (if ON, cap escalation per config)

Rent TDS 194-IB: from tax config — if monthly rent > threshold, cash inflow is gross*(1−tds_pct), and TDS accumulates as credit.

3.11 ExitPlan & MTM

allow_exit: bool, candidate_exit_months: int[]

brokerage_sell_pct, transfer_charges_on_sale

indexation_toggle: bool, sec54_54f_reinvest_toggle: bool

MTM toggles: include_cgt_in_mtm: true, include_prepayment_penalty_in_mtm: true

4) Price, Rent & Interiors — Calculations
4.1 Appreciation

Base 
𝐴
A = sum of appreciating heads (interiors excluded by default).
Monthly growth 
𝑔
=
𝐺
/
12
g=G/12 from annual CAGR 
𝐺
G. Apply shocks 
𝛿
𝑡
δ
t
	​

 linearly across windows.
Optionally apply age & legal discounts.

4.2 Interiors Depreciation (non-cash)

Straight-line over Y=10 years with residual 
𝑟
=
20
%
r=20%:

MonthlyDep
=
Capex
⋅
(
1
−
𝑟
)
12
𝑌
MonthlyDep=
12Y
Capex⋅(1−r)
	​

4.3 Rent

Rent paid during UC wait = escalated current_rent_per_month.

Rent saved after move-in equals the rent you would have paid (with escalation).

Rent received (yield-linked):

𝑅
𝑡
𝑟
𝑒
𝑐
𝑣
=
yield%
12
⋅
𝑉
𝑡
⋅
(
1
−
vacancy%
)
⋅
collection%
R
t
recv
	​

=
12
yield%
	​

⋅V
t
	​

⋅(1−vacancy%)⋅collection%

Rent TDS (194-IB): if gross monthly rent > threshold, cash inflow = gross*(1−tds_pct); tds_pct*gross added to TDS credits.

5) Loans — Disbursal, EMI/Pre-EMI, Floating Rates, Prepayment
5.1 LTV & Disbursal

Agreement ex-GST base:

𝐵
=
BSP
+
PLC
+
parking
+
club
+
other capital heads
B=BSP+PLC+parking+club+other capital heads

Cap:

MaxLoan
=
min
⁡
(
ltv%
⋅
𝐵
,
 RBI_Tier_Cap(ticket)
)
MaxLoan=min(ltv%⋅B, RBI_Tier_Cap(ticket))

At stage 
𝑠
s with financeable demand 
Δ
𝑠
Δ
s
	​

:

disbursal
𝑠
=
min
⁡
(
Δ
𝑠
,
 MaxLoan
−
bank_paid
,
 
StageCap
𝑠
⋅
𝐵
−
bank_paid
)
disbursal
s
	​

=min(Δ
s
	​

, MaxLoan−bank_paid, StageCap
s
	​

⋅B−bank_paid)

Remainder is margin (cash).

5.2 Pre-EMI & EMI

Monthly rate 
𝑖
=
roi
𝑡
/
12
i=roi
t
	​

/12.

Pre-EMI: interest on disbursed principal till EMI start.

EMI:

EMI
=
𝑃
⋅
𝑖
(
1
+
𝑖
)
𝑛
(
1
+
𝑖
)
𝑛
−
1
EMI=P⋅
(1+i)
n
−1
i(1+i)
n
	​

5.3 Floating Rates

If floating:

roi
𝑡
=
index
𝑡
+
spread
roi
t
	​

=index
t
	​

+spread

On index change:

AdjustTenure (default): keep EMI, recompute remaining tenure.

AdjustEMI: keep tenure, recompute EMI.

5.4 MRTA (Loan Insurance)

UpfrontBundled: add premium to principal before EMI calc.

Annual: add recurring premium outflow each year.

5.5 Prepayment Policy

If surplus after buffer and obligations, compare loan rate vs alt post-tax return. If spread ≥ threshold: prepay (tenure reduction default). Respect prepayment penalties per bank/product config (often 0 for floating).

6) Legal & Due-diligence Effects

OC gating: cannot move in / let out until OC (if gating ON).

B-Khata/OC pending: apply valuation discount (toggle) and ROI spread/eligibility warnings.

Buyer late payment penalty (BBA): for unpaid builder demand after grace:

Penalty
𝑡
=
Outstanding
×
penalty_p.a.
12
Penalty
t
	​

=Outstanding×
12
penalty_p.a.
	​


Leasehold: post annual_ground_rent yearly.

7) Funding & Liquidity (Shortfall Engine)
7.1 Required Buffer (one-shot pre-fund)

Let cumulative balance 
𝐵
𝑘
=
𝐿
0
+
∑
𝑡
=
1
𝑘
NCF
𝑡
B
k
	​

=L
0
	​

+∑
t=1
k
	​

NCF
t
	​

.

RequiredExtraAtStart
=
max
⁡
(
0
,
 
−
min
⁡
𝑘
𝐵
𝑘
)
RequiredExtraAtStart=max(0, −
k
min
	​

B
k
	​

)
7.2 Planned Top-ups

Given months 
𝜏
𝑗
τ
j
	​

: in each segment, size top-up so segment’s running min ≥ 0 after earlier injections.

7.3 Auto Funding Stack (default order)

If 
𝐿
𝑡
<
0
L
t
	​

<0:

Liquid → 2) MF redemption (FIFO) (compute CGT) → 3) OD draw (respect limit; accrue interest) → 4) HL top-up (UC only; within LTV & stage caps).

7.4 OD Sweep vs Invest (OverdraftLoan)

If 
(
roi
𝑡
−
alt_return
)
≥
threshold
(roi
t
	​

−alt_return)≥threshold → sweep surplus to OD; else invest.

8) Taxes (Per-Person; Config-Driven)

The engine is config-driven (tax slabs, 24(b) caps, 80C caps, CII, CGT rates, TDS rules). It does not “assert law”; it computes per supplied configs.

8.1 Income from House Property (IHP)

Let-out (annual):

IHP
=
GrossRent
−
VacancyLoss
−
MunicipalTaxes
−
30
%
−
AllowedInterest
IHP=GrossRent−VacancyLoss−MunicipalTaxes−30%−AllowedInterest

Self-occupied: NAV≈0; allow Sec 24(b) interest per cap & regime. Set-off cap (e.g., ₹2L) & carry-forward years from config.
Pre-construction interest (if enabled) deducted 1/5 each year for 5 years post completion (cap-aware).
Allocate interest & principal by co-owner share if co-borrowers.

8.2 Section 80C (Principal)

Per person up to cap; allocate by co-borrower share and remaining 80C headroom.

8.3 HRA (optional)

Compute only if salary inputs exist; otherwise off.

8.4 TDS

Purchase (194-IA): 1% of consideration (threshold & rate from config) — outflow at purchase; record TDS credit.

Rent (194-IB): if monthly rent > threshold, *cash inflow = gross(1−tds_pct)**; TDS credit recorded.

8.5 Capital Gains (Sale/MTM)
SaleNet
=
SalePrice
⋅
(
1
−
brokerage%
)
−
transfer
SaleNet=SalePrice⋅(1−brokerage%)−transfer
COA
=
BSP
+
capital heads
+
(
interiors if capitalized toggle
)
+
(
stamp/reg if toggle
)
COA=BSP+capital heads+(interiors if capitalized toggle)+(stamp/reg if toggle)

STCG/LTCG by holding period (config). If LTCG & indexation toggle ON → apply CII.

Sec 54/54F reinvestment toggle.

Safe harbor / guidance value (50C/43CA/56(2)(x)): if enabled and deviation > tolerance, use deemed value for CGT (seller) and compute buyer income (optional).

Allocate CGT across persons by share.

9) Exits & MTM
9.1 Actual Exit at month 
𝑒
e

Compute SaleNet_e.

Compute CGT Tax_e (STCG/LTCG; indexation; 54/54F).

Repay loans & OD; include prepayment penalty if applicable.

Post net exit cash at 
𝑒
e.

Stop future flows for the sold asset.

9.2 MTM at eval 
𝑇
T

Assume sale at 
𝑉
𝑇
V
T
	​

 → net after brokerage/transfer, loan closure + penalties, CGT (toggles) → 
𝑉
^
𝑇
V
T
	​

.
Use as notional inflow in MTM XIRR.

10) Outputs (Engine Must Produce)

KPI Cards

Total Outflows / Inflows / Net

Net Value today (
𝑉
^
𝑇
+
𝐿
𝑇
V
T
	​

+L
T
	​

)

Realized XIRR; MTM XIRR

Required buffer at start

Liquidity Panel

Min cash balance & month

Peak OD used; months in OD; OD interest cost

Funding Waterfall (cumulative: Liquid / MF / OD / HL top-up)

Tax Panel (per person)

IHP result; 24(b) used; pre-construction interest 1/5; 80C used; HRA used; CGT at exits; TDS credits

Exit Sensitivity: for candidate months → Net after tax & XIRR

Narrative Summary (top 5 sentences explaining drivers)

Key Events Timeline (possession, OC, stage spikes, biggest demand, interest reset, exit)

Risk Flags (B-Khata, OC pending/gate, negative equity months, unresolved shortfalls, state-rule mismatches)

11) Onboarding & Initialization (Existing Asset Hydration)

For a property bought earlier (e.g., 2019):

Inputs: original_purchase_date, original_purchase_cost, stamp/reg, interiors (with depreciation), loan_start_date, current_outstanding_principal, current_emi, remaining_tenure, current_market_value.

Initialize loan state as of start_date (t0).

Initialize COA (and indexed cost up to t0 for CGT previews).

Set 
𝑉
0
V
0
	​

 to current market value or recompute from appreciation base & shocks if desired.

12) Validations & Edge Cases

OC gating; B-Khata ROI spread + valuation warning; late-payment penalty on overdue demands.

Negative equity flagged (any 
𝑉
𝑡
−
𝐷
𝑡
<
0
V
t
	​

−D
t
	​

<0).

State mismatch prompt (stamp/registration).

Rate resets respect frequency and policy; recompute EMI/tenure.

Rent control: cap escalation if toggle ON.

Sanction risk: show scenario where final_sanctioned_amount = (1−risk_pct)*expected.

Eligibility (optional): FOIR/DTI, building age tenure caps → warnings (not blockers unless user opts).

13) QA & Golden Scenarios

Unit tests: EMI math; floating resets; AdjustEMI vs AdjustTenure; OD sweep vs invest; BBA penalty; OC gating; co-owner tax; leasehold ground rent; MF FIFO CGT; XIRR roots; CII indexation; balance transfer; MRTA bundling; rent TDS; sanction risk.

Goldens: RTM self-use; UC 20/30/50; subvention; upgrade (rent paid → rent saved); sell-1-buy-2; negative equity shock; B-Khata with NBFC spread; guidance value safe-harbor breach case; MRTA annual vs bundled.

14) External Configs (All Required at Runtime)

Schemas are production-ready and define validation contracts.

14.1 state_rules.json — Stamp/Registration, Safe Harbor, Leasehold

(JSON Schema provided earlier; include file as per that schema)

14.2 city_benchmarks.json — Yield, Maintenance, CAGR by Segment

(JSON Schema provided earlier)

14.3 tax_laws_FY.json — Slabs, 24(b)/80C, IHP, CGT, CII, TDS, MF tax

(JSON Schema provided earlier; one file per FY, engine picks by month → FY)

14.4 rate_index_path.json — REPO/MCLR curves; reset frequency

(JSON Schema provided earlier)

14.5 bank_policies.json — Product defaults, LTV tiers, stage caps, fees

(JSON Schema provided earlier)

Note: All percent values are in % units. Engine must log hash of each config in outputs.

15) UX Requirements (Engine-Agnostic but Binding)
15.1 Modes

Simple Mode (default): Property price, down payment, loan amount & tenure, interest rate, rent, city. Auto-fill city/state/FY defaults.

Detailed Mode: Full control over all inputs and toggles.

15.2 Key Drivers Panel (Interactive What-If)

Real-time sliders/inputs (3–4): Interest rate, Down payment, CAGR, Monthly rent. Changing any re-runs the engine and updates KPIs instantly.

15.3 Scenario Comparison View

Users can save scenarios and compare up to 3 side-by-side (XIRR, Net Value, Required Buffer, Peak OD Used, Min Cash Month). Highlight best by chosen metric.

15.4 Explainers & Tooltips

“Why this default?” shows source (city/state/TY config).

Every KPI has a “What is this?” link to a popover with one-line formula and a numeric example.

16) API Contracts (Illustrative; for backend service)
16.1 Calculate

POST /calculate_scenario
Request body: full Portfolio object (JSON) + inline overrides to config IDs if needed
Response:

{
  "kpis": {
    "total_outflows": 0,
    "total_inflows": 0,
    "net_cash": 0,
    "net_value_today": 0,
    "xirr_realized": 0.0,
    "xirr_mtm": 0.0,
    "required_buffer": 0,
    "min_cash_month": 0,
    "peak_od_used": 0
  },
  "liquidity_panel": {...},
  "funding_waterfall": {...},
  "tax_panel": {...},
  "exit_sensitivity": [...],
  "narrative_summary": ["..."],
  "key_events": [{"month":33,"description":"Possession - pre-EMI ends; rent saved starts"}],
  "risk_flags": ["OC pending", "B-Khata"],
  "ledger": [{"date":"2026-01-05","label":"Pre-EMI","amount":-23450,"reason":"..."}],
  "config_hashes": {...}
}

16.2 Sensitivity Runner

POST /run_sensitivity → request per sensitivity_runner.json schema; response returns tornado/ scenario tables + derived narratives.

16.3 CA Export

POST /export_ca → returns a zipped bundle (CSV + JSON) with a ca_export_manifest.json.

17) Sensitivity & Scenario Runner (Add-On A, Included)

Objectives: quantify driver impacts; provide tornado bars and scenario grids; optional spider chart.

Inputs: base config; one_way_sensitivities[] (JSONPath, deltas, modes), optional scenario_grid or full-factorial multi_factor_orthogonal, metrics to track, anchor metric, eval month.

Algorithm:

Run base; for each sensitivity delta → clone, patch, run, record change in anchor metric; sort by absolute impact → Tornado.

For scenario grids/factorials → patch sets, run, collect KPIs into table (optionally normalize for spider).

Outputs:

Tornado dataset: [{label, low_value, high_value, base_value, unit_hint}]

Scenario table: [{scenario_name, patches_applied, KPIs}]

Narrative sentences (“+1.0pp rate ↓ Net Value by ₹12.6L”)

Schema: sensitivity_runner.json (already provided).

18) “CA Export” Tax Pack (Add-On B, Included)

Files:

summary_FY.csv, property_IHP_FY.csv, property_sale_CGT.csv, mf_redemptions.csv, tds_credits.csv, ledger_master.csv, assumption_snapshot.json.
Manifest schema: ca_export_manifest.json (already provided).
Checks: ledger sums = summary; recompute CGT and FIFO externally matches engine; include config hashes.

19) Defaults (Smart, with Rationale)
Field	Default	Why
Posting day	5	Avoid month-end oddities
Maintenance escalation	7%	O&M inflation
Rent escalation	5%	Market norm
Rent yield	3%	Tier-1 mid segment
Vacancy	5%	Typical
Collection efficiency	98%	Minor loss
OD rate	12%	Unsecured OD
LTV	80%	Bank norm; capped by RBI tier
Exclude GST for LTV	True	Bank practice
Interiors depreciation	10y, 20% residual	Fit-out lifecycle
Alt after-tax return	6%	Conservative FI
BBA late penalty	18% p.a.	Common BBA
Letting brokerage	1 month	Market practice
PM fee	8% of rent	If using PM
Sanction risk	10%	Stress margin
Rent TDS (194-IB)	Threshold, 5%	Cashflow realism

All surfaced with tooltips and source (city/state/FY configs).

20) Worked Example Mapping (Your 2019→2025 Case)

Hydrate the 2019 RTM asset at t0 (current EMI ₹50k; outstanding principal; current market value ₹2.70Cr; interiors depreciated 10y/20% residual).

Options:

Sell A → Buy two UCs (different plans, exits at super-structure vs +12m).

Sell A → Upgrade UC 3.7Cr (40/60, +3y); rent ₹60k/m until OC; rent saved after move-in.

Keep A → Buy one investment (let out; vacancy, PM fee, deposit, rent TDS).

Funding: pre-fund vs planned bonuses vs auto stack; OD vs invest spread rule.

Outputs: apples-to-apples Realized/MTM XIRR, Net Value today (after tax), liquidity stress, exit sensitivity.

21) Appendices — Key Equations (Quick Reference)

EMI: 
EMI
=
𝑃
𝑖
(
1
+
𝑖
)
𝑛
(
1
+
𝑖
)
𝑛
−
1
EMI=P
(1+i)
n
−1
i(1+i)
n
	​


Disbursal: 
min
⁡
(
Δ
𝑠
,
 MaxLoan
−
bank_paid
,
 
StageCap
𝑠
⋅
𝐵
−
bank_paid
)
min(Δ
s
	​

, MaxLoan−bank_paid, StageCap
s
	​

⋅B−bank_paid)
Required buffer: 
max
⁡
(
0
,
−
min
⁡
𝑘
(
𝐿
0
+
∑
𝑡
=
1
𝑘
NCF
𝑡
)
)
max(0,−min
k
	​

(L
0
	​

+∑
t=1
k
	​

NCF
t
	​

))
Rent received (yield): 
yield%
12
⋅
𝑉
𝑡
⋅
(
1
−
vacancy%
)
⋅
collection%
12
yield%
	​

⋅V
t
	​

⋅(1−vacancy%)⋅collection%
OD interest: 
od_bal
𝑡
−
1
⋅
od_rate
12
od_bal
t−1
	​

⋅
12
od_rate
	​


Interiors SL: 
Capex
⋅
(
1
−
𝑟
)
12
𝑌
12Y
Capex⋅(1−r)
	​


Sale net: 
Price
⋅
(
1
−
brokerage%
)
−
transfer
Price⋅(1−brokerage%)−transfer

Why MTM should include more than brokerage (final clarity)

Even if you don’t intend to sell, MTM should reflect true net realizable value today (brokerage, transfer charges, CGT, and loan closure penalties). Excluding these overstates comparability; two paths with the same market price can yield very different “cash if sold today.” We keep CGT & penalties ON by default (toggles exposed).

Final Readiness Statement

This v1.4 document is standalone, comprehensive, and developer-ready. It integrates:

Financial realism (term/OD loans, floating rate resets, prepayment, MRTA, sanction risk)

Legal/due-diligence (Khata, OC/CC, BBA penalties, leasehold, guidance value)

Portfolio and co-owner tax allocation (IHP, 24(b), 80C, HRA opt-in, CGT; rent TDS)

Funding engine (buffer, planned top-ups, auto stack with MF FIFO & OD)

Fair comparability (Realized vs MTM returns after tax & costs)

Add-Ons: Sensitivity Runner and CA Export pack

External configs with JSON Schemas (state, city, FY tax, rate paths, bank policies)

UX: Simple/Detailed, Interactive Key Drivers, Scenario Comparison, narratives & key events

API contracts and complete QA plan.