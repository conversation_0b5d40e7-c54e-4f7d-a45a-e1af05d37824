#!/usr/bin/env bash
set -euo pipefail

export PYTHONPATH="${PYTHONPATH:-.}"

# Example config paths (adjust to your local files)
export STATE_RULES_PATH="${STATE_RULES_PATH:-configs/state_rules.json}"
export CITY_BENCHMARKS_PATH="${CITY_BENCHMARKS_PATH:-configs/city_benchmarks.json}"
export TAX_LAWS_PATH="${TAX_LAWS_PATH:-configs/tax_laws.json}"
export RATE_INDEX_PATH="${RATE_INDEX_PATH:-configs/rate_index.json}"
export BANK_POLICIES_PATH="${BANK_POLICIES_PATH:-configs/bank_policies.json}"

# Open CORS for local testing; harden in prod
export CORS_ALLOW_ORIGINS="${CORS_ALLOW_ORIGINS:-*}"

# Show traces in responses for dev; set to 1 in prod to hide Python traces
export REDUCE_ERROR_TRACE="${REDUCE_ERROR_TRACE:-0}"

# Run server (reload for dev)
exec uvicorn server.main:app --host 0.0.0.0 --port 8000 --reload
