from decimal import Decimal as D
from rede.types import D as DD
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy, ExistingBasis, ExitSettings
from .helpers import run_engine

def test_s3_resale_existingbasis_sale_m36():
    p = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(200000))
    prop = Property(
        id="R1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(0)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(),
        existing_basis=ExistingBasis(purchase_total=DD(11_000_000), stamp_duty_paid=DD(700000), registration_fee_paid=DD(0)),
        exit=ExitSettings(sale_month=36, brokerage_pct=DD(1.5), buyer_tds_pct=DD(1), transfer_fee_fixed=DD(100000))
    )
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=48,
        persons=[p], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(200000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(), tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )
    res = run_engine(pf)
    ledger = res["ledger"]

    # Expect sale lines around M36: Proceeds (net TDS), Brokerage, Transfer, Property CGT
    assert any("Sale Proceeds" in l["label"] for l in ledger), "Expected realized sale proceeds"
    assert any("Brokerage on Sale" == l["label"] for l in ledger), "Expected brokerage outflow"
    assert any("Transfer Fee" == l["label"] for l in ledger), "Expected transfer fee outflow"
    assert any("Property CGT" in l["label"] for l in ledger), "Expected CGT on sale"
