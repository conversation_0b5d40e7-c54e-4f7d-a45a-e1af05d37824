from __future__ import annotations
from typing import Any, List, Dict, Optional
from pydantic import BaseModel, Field

# NOTE: Keep this file *engine-agnostic*. The engine returns dicts; this models
# what your API returns to the UI. We align names with the CLI/scripts.

class CalculateScenarioRequest(BaseModel):
    """
    POST /calculate_scenario

    The 'portfolio' payload is validated by the backend by parsing into the
    Portfolio model (pydantic) before calculation.
    """
    portfolio: Dict[str, Any]


class KPIBlock(BaseModel):
    """
    Aggregated KPIs returned by the engine/CLI.

    Canonical keys expected by our CLI harness and tests:
      - realized_xirr
      - mtm_xirr
      - min_liquid_balance
    We keep a few aliases for backward compatibility.
    """
    total_outflows: float = 0.0
    total_inflows: float = 0.0
    net_cash: float = 0.0

    # Net value (mark-to-market, includes cash + property MTM after debt)
    net_value_today: float = 0.0

    # Canonical names used in scripts/run_all_fixtures.sh
    realized_xirr: Optional[float] = Field(default=None, description="XIRR on realized cashflows")
    mtm_xirr: Optional[float] = Field(default=None, description="XIRR including MTM (paper) value")

    # Keep old aliases if some clients used xirr_realized / xirr_mtm
    xirr_realized: Optional[float] = Field(default=None, alias="xirr_realized")
    xirr_mtm: Optional[float] = Field(default=None, alias="xirr_mtm")

    # Liquidity
    required_buffer: Optional[float] = None  # minimum start buffer to avoid negative cash
    min_liquid_balance: Optional[float] = None  # what CLI expects
    min_cash_month: Optional[int] = None       # optional (some UIs prefer month index)

    # Risk/Utilisation
    peak_od_used: Optional[float] = None


class CalculateScenarioResponse(BaseModel):
    """
    Full response returned to the UI. Keep dicts for panels to avoid over-constraining.
    """
    kpis: KPIBlock
    liquidity_panel: Dict[str, Any] = Field(default_factory=dict)

    # Funding panel (the backend assembles a breakdown). Keep both keys;
    # some clients may refer to 'funding_waterfall', others to 'funding_panel'.
    funding_panel: Dict[str, Any] = Field(default_factory=dict)
    funding_waterfall: Dict[str, Any] = Field(default_factory=dict)

    tax_panel: Dict[str, Any] = Field(default_factory=dict)
    exit_sensitivity: List[Dict[str, Any]] = Field(default_factory=list)

    narrative_summary: List[str] = Field(default_factory=list)
    key_events: List[Dict[str, Any]] = Field(default_factory=list)
    risk_flags: List[str] = Field(default_factory=list)

    # Full ledger as list of plain dicts (stable for CSV/exports)
    ledger: List[Dict[str, Any]] = Field(default_factory=list)

    # Hashes of loaded configs (for provenance)
    config_hashes: Dict[str, str] = Field(default_factory=dict)


# -------------------------------
# Sensitivity Runner (Add-On A)
# -------------------------------

class SensitivityPatch(BaseModel):
    path: str   # JSONPath-like (our minimal syntax): $.properties[0].loan.initial_roi_annual_pct
    value: Any

class OneWaySensitivity(BaseModel):
    label: str
    path: str
    deltas: List[float]
    delta_mode: str  # "absolute" | "percent"
    min_value: float | None = None
    max_value: float | None = None
    unit_hint: str | None = None

class ScenarioGridItem(BaseModel):
    name: str
    patches: List[SensitivityPatch]

class MultiFactorLevel(BaseModel):
    factor: str           # label for UI ("Rate", "Rent", etc.)
    path: str
    mode: str             # "absolute" | "percent"
    levels: List[float]

class SensitivityRunnerSpec(BaseModel):
    one_way_sensitivities: List[OneWaySensitivity] = Field(default_factory=list)
    scenario_grid: List[ScenarioGridItem] | None = None
    multi_factor_orthogonal: List[MultiFactorLevel] | None = None

    metrics_to_track: List[str] = Field(
        default_factory=lambda: ["net_value_today", "realized_xirr", "mtm_xirr", "required_buffer", "min_liquid_balance"]
    )
    tornado_anchor_metric: str = "net_value_today"
    eval_month: int | None = None
