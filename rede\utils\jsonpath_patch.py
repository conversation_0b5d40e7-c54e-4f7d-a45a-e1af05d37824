from __future__ import annotations
from typing import Any, List, Dict, Tuple
import copy
import re

_PATH_RE = re.compile(r"""
    (?P<key>[^\[\]]+)?      # dict key (optional if starting with [index])
    (?:\[(?P<idx>\d+)\])?   # optional single list index [0]
""", re.X)

def _parse_path(path: str) -> List[Tuple[str | None, int | None]]:
    """
    Parse a minimal JSONPath-like string into [(key, idx), ...].
    Supports:
      $.a.b[0].c
      $[0].a
      $.properties[2].loan.initial_roi_annual_pct
    Not supported: wildcards, filters, multi-indexes.
    """
    if not path.startswith("$"):
        raise ValueError("JSONPath must start with '$'")
    path = path[1:]  # drop '$'
    if path.startswith("."):
        path = path[1:]
    parts: List[Tuple[str | None, int | None]] = []
    for token in path.split(".") if path else []:
        if token == "":
            continue
        # token can be 'key', 'key[3]', or '[0]'
        m = _PATH_RE.fullmatch(token)
        if not m:
            raise ValueError(f"Unsupported path token: {token}")
        key = m.group("key")
        idx = m.group("idx")
        parts.append((key if key else None, int(idx) if idx is not None else None))
    return parts

def _ensure_dict(obj: Any, key: str) -> dict:
    if not isinstance(obj, dict):
        raise TypeError(f"Expected dict to access key '{key}', got {type(obj).__name__}")
    if key not in obj or obj[key] is None:
        obj[key] = {}
    if not isinstance(obj[key], (dict, list)):
        # Allow overwriting scalars with dict if path continues
        obj[key] = {}
    return obj[key]

def _ensure_list(obj: Any, key: str | None, idx: int) -> list:
    """
    If key is None, obj itself must be a list and we index into it.
    If key is a str, obj must be a dict and obj[key] must be a list (create if absent).
    """
    target = obj
    if key is not None:
        if not isinstance(obj, dict):
            raise TypeError(f"Expected dict to access key '{key}', got {type(obj).__name__}")
        if key not in obj or obj[key] is None:
            obj[key] = []
        target = obj[key]
    if not isinstance(target, list):
        # overwrite scalars/dicts if needed to satisfy path
        target = obj[key] = [] if key is not None else target
        if not isinstance(target, list):
            # if still not list (key is None branch), enforce type
            raise TypeError("Expected list in JSONPath index step")

    # Grow list if needed
    if idx >= len(target):
        target.extend([None] * (idx + 1 - len(target)))
    return target

def _set_path(root: Any, path: str, value: Any) -> Any:
    obj = root
    parts = _parse_path(path)
    if not parts:
        # Path is '$' only: replace the whole object
        return value

    cur = obj
    for i, (key, idx) in enumerate(parts):
        is_last = (i == len(parts) - 1)

        if idx is None:
            # dict access
            if key is None:
                raise ValueError("Path component with neither key nor index is invalid")
            if is_last:
                if not isinstance(cur, dict):
                    raise TypeError(f"Expected dict at final step for key '{key}'")
                cur[key] = value
                return obj
            # ensure dict for next step
            cur = _ensure_dict(cur, key)
        else:
            # list access (with optional dict key)
            target_list = _ensure_list(cur, key, idx)
            if is_last:
                target_list[idx] = value
                return obj
            # step into the list element
            nxt = target_list[idx]
            if nxt is None:
                nxt = {}
                target_list[idx] = nxt
            cur = nxt
    return obj

def apply_patches(obj: Any, patches: List[Dict[str, Any]]) -> Any:
    """
    Apply JSONPath-like patches used by the Sensitivity Runner (Add-On A).
    Each patch: {"path": "$.properties[0].loan.initial_roi_annual_pct", "value": 9.25}
    Supported features:
      - Root '$'
      - Dot navigation
      - Single list index [n]
      - Creating intermediate dicts / growing lists as needed
    Not supported: wildcards, filters, multi-index slices.
    The input object is deep-copied; original is untouched.
    """
    new_obj = copy.deepcopy(obj)
    for p in patches or []:
        path = p.get("path")
        if not isinstance(path, str):
            raise ValueError("Patch must have a string 'path'")
        new_obj = _set_path(new_obj, path, p.get("value"))
    return new_obj
