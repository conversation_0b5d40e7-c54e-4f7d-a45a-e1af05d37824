from decimal import Decimal as D
from datetime import date
from rede.engine.exit_engine import ExitEngine
from rede.engine.tax_engine import TaxEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import Property, PropertyHeads, PaymentPlan, PropertyOwnershipCosts, PropertyOccupancy, ExistingBasis, ExitSettings
from rede.engine.ledger import Ledger

def _ctx_pf_prop_for_sale():
    class _Cfg: content = {
        "property_cgt": {"stcg_rate_pct": 30, "ltcg_rate_pct": 20, "holding_days_ltcg": 730},
        "cii": {"FY2021-22": 317, "FY2025-26": 360}
    }
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    p = Person(id="p1", name="A", tax_regime="old", shareholdings={}, liquid_cash=D(0))
    # Resale/existing basis: purchase total 50L; stamp 0; reg 0; improvements 0 -> total 50L
    prop = Property(
        id="prop1", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=D(0)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(),
        existing_basis=ExistingBasis(purchase_total=D(5_000_000)),
        exit=ExitSettings(brokerage_pct=D(1), transfer_fee_fixed=D(0), buyer_tds_pct=D(1), cgt_pay_timing="immediate")
    )
    pf = Portfolio(currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=1,
                   persons=[p], properties=[prop], mf_holdings=[],
                   user_starting_liquid_cash_by_person={"p1": D(0)},
                   alt_invest_return_after_tax_pct=D(6), loan_sanction_risk_pct=D(0),
                   funding_strategy=FundingStrategy(), tax_settings_global=TaxSettingsGlobal(),
                   configs=RuntimeConfigs("","","","",""))
    return ctx, pf, prop

def test_realized_sale_with_cgt_and_loan_closure():
    ctx, pf, prop = _ctx_pf_prop_for_sale()
    tax = TaxEngine(ctx, pf)
    exit_engine = ExitEngine()
    ledger = Ledger()

    sale_dt = date(2025, 9, 5)
    gross = D(10_000_000)  # 1 Cr
    brokerage = gross * D("0.01")  # 1% = 1L
    transfer = D(0)
    outstanding = D(2_000_000)  # 20L principal
    # CGT: holding > 24 months assumed (purchase date should be earlier, TaxEngine uses Portfolio.start by default if not supplied)
    # Indexed cost: we’ll let TaxEngine handle with CII if it can; else fallback yields conservative number.

    acq_total = exit_engine.resolve_acquisition_basis(prop, D(0))  # from ExistingBasis: 50L

    summary = exit_engine.realized_sale(
        posting=sale_dt, t=0, prop=prop, current_value=gross,
        outstanding_principal=outstanding, acquisition_cost_total=acq_total,
        ledger=ledger, tax_engine=tax
    )
    # Check presence of key lines
    labels = [l.label for l in ledger.lines]
    assert any("Sale Proceeds" in s for s in labels)
    assert any("Brokerage on Sale" == s for s in labels)
    assert any("Loan Closure - Principal" == s for s in labels)
    assert any("Property CGT" in s for s in labels), "CGT line should be present (immediate)"

    # Buyer TDS reduces cash inflow; ensure it’s < gross - brokerage - transfer
    inflow = [l for l in ledger.lines if "Sale Proceeds" in l.label][0].amount
    assert float(inflow) < float(gross - brokerage - transfer)

    # Principal closure equals outstanding
    loan_close = [l for l in ledger.lines if l.label == "Loan Closure - Principal"][0].amount
    assert float(loan_close) == -float(outstanding)
