#!/usr/bin/env python3
import argparse, json
from pathlib import Path
from decimal import Decimal

from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio

# Minimal tax config defaults (aligns with tests)
DEFAULT_TAX = {
    "tds_rules": {"rent_194IB": {"monthly_threshold": 50000, "rate_pct": 5}},
    "mf_tax": {
        "equity": {
            "holding_days_ltcg": 365,
            "stcg_rate_pct": 15,
            "ltcg_rate_pct": 10,
            "ltcg_annual_exemption": 100000,
            "indexation_allowed": False
        },
        "debt": {
            "holding_days_ltcg": 1095,
            "stcg_rate_pct": 30,
            "ltcg_rate_pct": 20,
            "ltcg_annual_exemption": 0,
            "indexation_allowed": False
        }
    },
    "property_cgt": {"stcg_rate_pct": 30, "ltcg_rate_pct": 20, "holding_days_ltcg": 730},
    "cii": {
        "FY2019-20": 289, "FY2020-21": 301, "FY2021-22": 317, "FY2022-23": 331,
        "FY2023-24": 348, "FY2024-25": 348, "FY2025-26": 360
    }
}

class _Cfg:
    def __init__(self, content): self.content = content

def load_portfolio(path: str) -> Portfolio:
    data = json.loads(Path(path).read_text(encoding="utf-8"))
    return Portfolio.parse_obj(data)

def main():
    ap = argparse.ArgumentParser(description="Run Real Estate Engine on a JSON fixture.")
    ap.add_argument("fixture", help="Path to JSON fixture")
    ap.add_argument("-n", "--top-ledger", type=int, default=20, help="Number of ledger lines to show (text mode)")
    ap.add_argument("--json", action="store_true", help="Print compact JSON result (KPIs, flags, narratives[:10], top ledger)")
    ap.add_argument("--json-out", type=str, default="", help="Write full JSON result to file path")
    args = ap.parse_args()

    pf = load_portfolio(args.fixture)

    ctx = EngineContext()
    ctx.tax_laws_fy = _Cfg(DEFAULT_TAX)

    eng = TimelineEngine(ctx)
    eng.ctx.portfolio = pf
    eng.run()
    res = eng.result()

    if args.json:
        # Compact JSON for machine parsing in run_all_fixtures.sh
        compact = {
            "fixture": str(args.fixture),
            "kpis": res.get("kpis", {}),
            "risk_flags": res.get("risk_flags", []),
            "narrative_summary": (res.get("narrative_summary", [])[:10]),
            "ledger_top": (res.get("ledger", [])[:args.top_ledger]),
        }
        print(json.dumps(compact, ensure_ascii=False))
        return

    if args.json_out:
        Path(args.json_out).write_text(json.dumps(res, ensure_ascii=False, indent=2), encoding="utf-8")

    # Human-readable text mode
    print("\n=== KPIs ===")
    for k, v in res["kpis"].items():
        print(f"{k:28s}: {v}")

    print("\n=== Risk Flags ===")
    if res["risk_flags"]:
        for rf in res["risk_flags"]:
            print(f"- {rf}")
    else:
        print("(none)")

    print("\n=== Narratives (first 10) ===")
    for s in res["narrative_summary"][:10]:
        print(f"- {s}")

    print(f"\n=== Ledger (top {args.top_ledger}) ===")
    for line in res["ledger"][:args.top_ledger]:
        print(f"{line['date']} | {line.get('property_id','-'):6s} | {line['label']:<40s} | {line['amount']:>12}")

if __name__ == "__main__":
    main()
