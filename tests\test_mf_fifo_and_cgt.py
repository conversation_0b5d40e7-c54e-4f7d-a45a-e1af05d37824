import datetime as dt
from decimal import Decimal as D

from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal, MFHoldingLot
from rede.models.property import Property, PropertyHeads, PaymentPlan, PaymentEvent, PaymentTrigger, PropertyOwnershipCosts, PropertyOccupancy
from rede.models.enums import PaymentTriggerType
from rede.types import D as DD

def _ctx_tax():
    # FY = 2025-26 begins 2025-04-01
    tax_laws = {
        "tds_rules": {"rent_194IB": {"monthly_threshold": 50000, "rate_pct": 5}},
        "mf_tax": {
            "equity": {"holding_days_ltcg": 365, "stcg_rate_pct": 15, "ltcg_rate_pct": 10, "ltcg_annual_exemption": 100000, "indexation_allowed": False}
        },
        "property_cgt": {"stcg_rate_pct": 30, "ltcg_rate_pct": 20, "holding_days_ltcg": 730},
        "cii": {"FY2024-25": 348, "FY2025-26": 360}
    }
    class _Cfg: content = tax_laws
    ctx = EngineContext(); ctx.tax_laws_fy = _Cfg()
    return ctx

def _portfolio_need_exactly_first_lot_net():
    p = Person(id="p1", name="Owner", tax_regime="old", shareholdings={}, liquid_cash=DD(0))
    # Demand in April 2025 equal to MF Lot1 net proceeds to avoid OD
    # Lot1: units=1000, nav=100, exit_load=1% -> net=1000*100*0.99 = 99,000
    # Create builder event base=99,000 (financeable_by_bank=False)
    ev = PaymentEvent(
        name="Demand",
        stage="Booking",
        trigger=PaymentTrigger(type=PaymentTriggerType.month, month=0),
        percent_of_base=DD(100),
        gst_pct=DD(0),
        financeable_by_bank=False
    )
    prop = Property(
        id="prop1",
        city="Mumbai",
        heads=PropertyHeads(base_price_ex_gst=DD(99000)),
        plan=PaymentPlan(events=[ev]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(),
        loan=None
    )
    mf1 = MFHoldingLot(
        id="L1", scheme_name="EQ-Lot1", asset_type="equity",
        units=1000, nav_acquired=80, acquired_date="2023-03-01",
        current_nav=100, exit_load_pct=DD(1)
    )
    # Add second lot to test FIFO is NOT touched
    mf2 = MFHoldingLot(
        id="L2", scheme_name="EQ-Lot2", asset_type="equity",
        units=500, nav_acquired=120, acquired_date="2024-12-01",
        current_nav=110, exit_load_pct=DD(1)
    )
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=12,
        persons=[p], properties=[prop],
        mf_holdings=[mf1, mf2],
        user_starting_liquid_cash_by_person={"p1": DD(0)},
        alt_invest_return_after_tax_pct=DD(6),
        loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), od_annual_rate_pct=DD(12), mf_fifo_enabled=True),
        tax_settings_global=TaxSettingsGlobal(),
        configs=RuntimeConfigs("","","","","")
    )
    return pf

def test_mf_fifo_redemption_and_fy_cgt_line():
    ctx = _ctx_tax()
    pf = _portfolio_need_exactly_first_lot_net()
    eng = TimelineEngine(ctx); eng.ctx.portfolio = pf
    eng.run()
    res = eng.result()
    ledger = res["ledger"]

    # Expect exactly one MF redemption line for Lot1; Lot2 untouched
    mf_lines = [l for l in ledger if l["category"] == "funding_mf"]
    assert mf_lines, "Expected MF redemption"
    assert any("EQ-Lot1" in l["label"] for l in mf_lines), "FIFO should redeem Lot1 first"
    assert not any("EQ-Lot2" in l["label"] for l in mf_lines), "Lot2 should not be redeemed"

    # No OD draw, since MF covered the need
    assert not any(l for l in ledger if l["label"] == "OD Draw"), "OD should not be used"

    # CGT line at FY end (2026-03-31 for FY2025-26)
    cgt_lines = [l for l in ledger if l["label"].startswith("MF CGT (FY2025-26)")]
    assert cgt_lines, "Expected MF CGT payment at FY end"
    # The amount should be negative (tax outflow)
    assert all(D(str(l["amount"])) < 0 for l in cgt_lines)
