from __future__ import annotations
from pydantic import BaseModel, <PERSON>
from typing import Dict
from decimal import Decimal

Percent = Decimal

class RatePoint(BaseModel):
    # Either FY label or YYYY-MM keys in the series below will map to these rates
    index_pct: Percent

class RateIndexSeries(BaseModel):
    """
    Optional external index series for floating loans.
    Keys can be FY labels (e.g., 'FY2025-26') or 'YYYY-MM'.
    """
    series: Dict[str, RatePoint] = Field(default_factory=dict)

def parse_rate_index(content: dict) -> RateIndexSeries:
    if not isinstance(content, dict):
        content = {}
    if "series" not in content:
        # allow raw mapping { "2025-04": {"index_pct": 8.75}, ... }
        content = {"series": content}
    return RateIndexSeries.parse_obj(content)
