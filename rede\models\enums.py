from enum import Enum

class TaxRegime(str, Enum):
    OLD="old"
    NEW="new"

class KhataType(str, Enum):
    A="A-Khata"
    B="B-Khata"
    NA="NA"

class OCStatus(str, Enum):
    NOT_APPLIED="NotApplied"
    APPLIED="Applied"
    GRANTED="Granted"

class LandTenure(str, Enum):
    FREEHOLD="Freehold"
    LEASEHOLD="Leasehold"

class AmountType(str, Enum):
    PERCENT="percent"
    FIXED="fixed"

class TriggerType(str, Enum):
    MONTH_OFFSET="month_offset"
    STAGE="stage"
    POSSESSION="possession"
    HANDOVER="handover"
    OC_RECEIVED="oc_received"
    CUSTOM_DATE="custom_date"

class LoanProductType(str, Enum):
    TERM="Term Loan"
    OD="Overdraft"

class RateIndex(str, Enum):
    REPO="REPO"
    MCLR="MCLR"
    FIXED="FIXED"

class RateChangePolicy(str, Enum):
    ADJUST_TENURE="AdjustTenure"
    ADJUST_EMI="AdjustEMI"
