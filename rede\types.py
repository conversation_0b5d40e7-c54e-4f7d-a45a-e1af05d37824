"""
Numeric type helpers shared across the engine.

- Money, Percent, MonthIndex are NewTypes over Decimal/int for clarity in signatures.
- D(value) wraps Decimal(str(value)) for safe creation without FP noise.
"""

from decimal import Decimal, getcontext
from typing import NewType

# Set safe precision for money math (tune if needed)
getcontext().prec = 28

Money = NewType("Money", Decimal)
Percent = NewType("Percent", Decimal)  # Always in 0–100 units per v1.4
MonthIndex = NewType("MonthIndex", int)

def D(value) -> Decimal:
    """Helper to create Decimals from int/str/float safely."""
    return Decimal(str(value))

__all__ = ["Money", "Percent", "MonthIndex", "D"]
