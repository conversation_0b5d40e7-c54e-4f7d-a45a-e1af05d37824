class RedeError(Exception):
    """Base error for the Real Estate Decision Engine."""

class ConfigLoadError(RedeError):
    """Raised when external configuration cannot be loaded or is invalid."""

class ValidationError(RedeError):
    """Raised when user input (Portfolio) fails validation."""

class CalculationError(RedeError):
    """Raised when a calculation step encounters an unrecoverable condition."""
