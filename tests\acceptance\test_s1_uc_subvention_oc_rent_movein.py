from decimal import Decimal as D
from rede.types import D as DD
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import (
    Property, PropertyHeads, PaymentPlan, PaymentEvent, PaymentTrigger,
    PropertyOwnershipCosts, PropertyOccupancy
)
from rede.models.enums import PaymentTriggerType
from .helpers import run_engine

def test_s1_uc_subvention_oc_rent_movein():
    # UC 20/30/50; possession M30, OC M36; subvention covers pre-EMI until OC
    # Rent ₹60k while waiting, 5% annual escalation; move-in at OC (M36)
    p = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(2_000_000))
    events = [
        PaymentEvent(name="Booking 20%", stage="Booking", trigger=PaymentTrigger(type=PaymentTriggerType.month, month=0),
                     percent_of_base=DD(20), gst_pct=DD(5), financeable_by_bank=True),
        PaymentEvent(name="SuperStructure 30%", stage="Structure", trigger=PaymentTrigger(type=PaymentTriggerType.month, month=18),
                     percent_of_base=DD(30), gst_pct=DD(5), financeable_by_bank=True),
        PaymentEvent(name="Possession 50%", stage="Possession", trigger=PaymentTrigger(type=PaymentTriggerType.month, month=30),
                     percent_of_base=DD(50), gst_pct=DD(5), financeable_by_bank=True,
                     subvention={"coverage": "pre_emi", "end_trigger": "oc_received"})
    ]
    heads = PropertyHeads(
        base_price_ex_gst=DD(20_000_000),  # 2 Cr
        tds_buy_pct=DD(1),                  # 194-IA on purchase base
        ltv_pct=DD(75)                      # bank LTV % ex-GST (engine respects in disbursal)
    )
    occ = PropertyOccupancy(
        move_in_month=36, let_out_month=None,
        rent_paid_while_waiting=DD(60000), rent_escalation_annual_pct=DD(5)
    )
    prop = Property(
        id="P1", city="Bengaluru",
        heads=heads, plan=PaymentPlan(events=events),
        ownership_costs=PropertyOwnershipCosts(annual_property_tax=DD(15000), maintenance_monthly=DD(6000), maintenance_escalation_pct=DD(5)),
        occupancy=occ
    )
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=48,
        persons=[p], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(2_000_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(0), od_annual_rate_pct=DD(12), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )

    res = run_engine(pf)
    ledger = res["ledger"]
    # Expect: Rent paid lines before OC month; Rent saved lines after move-in (post OC)
    rent_paid = [l for l in ledger if l["label"].startswith("Rent Paid")]
    assert rent_paid, "Expected rent paid before OC"
    rent_saved = [l for l in ledger if "Rent Saved" in l["label"]]
    assert rent_saved, "Expected rent saved after move-in (post OC)"

    # Expect purchase TDS lines
    assert any("Purchase TDS 194-IA" in l["label"] for l in ledger), "Expected purchase TDS postings"

    # Pre-EMI should not hit cash (subvention=pre_emi until OC)
    assert not any("Pre-EMI" in l["label"] for l in ledger), "Pre-EMI should be covered under subvention until OC"

    # Narratives should include OC received and possession
    narr = res["narrative_summary"]
    assert any("OC received" in s for s in narr), "Expect OC narrative"
    assert any("Possession milestone" in s for s in narr), "Expect possession narrative"
