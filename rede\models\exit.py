from pydantic import BaseModel
from ..types import Money, Percent, D

class ExitSettings(BaseModel):
    """
    Realized exit configuration used by the timeline/exit engines and acceptance tests.
    """
    sale_month: int | None = None             # if None => MTM only
    brokerage_pct: Percent = D(1.5)
    transfer_fee_fixed: Money = D(0)
    buyer_tds_pct: Percent = D(1.0)           # Section 194-IA (buyer TDS)
    cgt_pay_timing: str = "immediate"         # "immediate" | "fy_end"
    include_cgt_in_mtm: bool = True
