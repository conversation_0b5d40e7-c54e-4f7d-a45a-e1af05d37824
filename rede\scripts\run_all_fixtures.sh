#!/usr/bin/env bash
set -euo pipefail

# Defaults
FIXDIR="${1:-tests/fixtures}"
PYBIN="${PYBIN:-python3}"
RUNPY="scripts/run_fixture.py"
LEDGER_TOP="${LEDGER_TOP:-10}"
JSON_OUT_DIR="${JSON_OUT_DIR:-}"

# Optional tools
if command -v jq >/dev/null 2>&1; then
  HAS_JQ=1
else
  HAS_JQ=0
fi

if [[ ! -f "$RUNPY" ]]; then
  echo "ERROR: $RUNPY not found. Run from repo root or set RUNPY env." >&2
  exit 1
fi

if [[ ! -d "$FIXDIR" ]]; then
  echo "ERROR: Fixture directory '$FIXDIR' not found." >&2
  exit 1
fi

# Pretty colors
BOLD=$(printf '\033[1m'); DIM=$(printf '\033[2m'); GREEN=$(printf '\033[32m'); YELLOW=$(printf '\033[33m'); RED=$(printf '\033[31m'); NC=$(printf '\033[0m')

printf "${BOLD}Running fixtures in %s${NC}\n" "$FIXDIR"
printf "${DIM}Using %s | jq=%s | top-ledger=%s${NC}\n\n" "$PYBIN" "$HAS_JQ" "$LEDGER_TOP"

# Header
printf "${BOLD}%-34s | %-12s | %-12s | %-10s | %-10s | %-10s | %-10s | %-3s${NC}\n" "Fixture" "Outflows(₹)" "Inflows(₹)" "Net(₹)" "RealXIRR" "MTMXIRR" "MinCash(₹)" "RF"
printf -- "---------------------------------------------------------------------------------------------------------------------------------------\n"

fail=0
total=0

for f in "$FIXDIR"/*.json; do
  [[ -e "$f" ]] || continue
  total=$((total+1))
  # JSON compact
  out=$("$PYBIN" "$RUNPY" "$f" --json --top-ledger "$LEDGER_TOP")
  if [[ -n "${JSON_OUT_DIR}" ]]; then
    mkdir -p "$JSON_OUT_DIR"
    echo "$out" > "${JSON_OUT_DIR}/$(basename "$f" .json).result.json"
  fi

  if [[ "$HAS_JQ" -eq 1 ]]; then
    k_out=$(echo "$out" | jq -r '.kpis.total_outflows // 0')
    k_in=$(echo "$out" | jq -r '.kpis.total_inflows // 0')
    k_net=$(echo "$out" | jq -r '.kpis.net_cash // 0')
    k_rxirr=$(echo "$out" | jq -r '.kpis.realized_xirr // "—"')
    k_mtmxirr=$(echo "$out" | jq -r '.kpis.mtm_xirr // "—"')
    k_min=$(echo "$out" | jq -r '.kpis.min_liquid_balance // 0')
    rf_count=$(echo "$out" | jq -r '.risk_flags | length')

    # Color if net < 0 or RFs exist
    color="$NC"
    if [[ "$rf_count" -gt 0 ]]; then color="$YELLOW"; fi
    if awk "BEGIN{exit !($k_net < 0)}"; then color="$RED"; fi

    printf "%-34s | %12.0f | %12.0f | %10.0f | %10s | %10s | %10.0f | %3d\n" \
      "$(basename "$f")" "$k_out" "$k_in" "$k_net" "$k_rxirr" "$k_mtmxirr" "$k_min" "$rf_count" | sed "s/^/$color/;s/$/$NC/"
  else
    # Fallback: no jq -> just print fixture name and run in text mode
    printf "%-34s | %s\n" "$(basename "$f")" "jq not found — showing text summary below:"
    "$PYBIN" "$RUNPY" "$f" -n "$LEDGER_TOP" || fail=$((fail+1))
    echo
  fi
done

printf "\n${BOLD}Done.${NC} Processed %d fixture(s).\n" "$total"
if [[ "$fail" -gt 0 ]]; then
  printf "${RED}%d fixture(s) failed.${NC}\n" "$fail"
  exit 1
fi

# Tip: to save JSON outputs, run:
#   JSON_OUT_DIR=artifacts ./scripts/run_all_fixtures.sh
