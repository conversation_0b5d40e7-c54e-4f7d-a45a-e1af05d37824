from dataclasses import dataclass
from typing import Dict
from decimal import Decimal as D
from datetime import date as _d

from rede.engine.timeline import TimelineEngine
from rede.engine.context import EngineContext

# Minimal wrapper so each test can quickly spin tax/config defaults
@dataclass
class MiniTaxCfg:
    content: Dict

DEFAULT_TAX = {
    # Rent TDS 194-IB (tenant deducts)
    "tds_rules": {"rent_194IB": {"monthly_threshold": 50000, "rate_pct": 5}},
    # MF CGT rules
    "mf_tax": {
        "equity": {
            "holding_days_ltcg": 365,
            "stcg_rate_pct": 15,
            "ltcg_rate_pct": 10,
            "ltcg_annual_exemption": 100000,
            "indexation_allowed": False
        },
        "debt": {
            "holding_days_ltcg": 1095,
            "stcg_rate_pct": 30,
            "ltcg_rate_pct": 20,
            "ltcg_annual_exemption": 0,
            "indexation_allowed": False
        },
    },
    # Property CGT (proxy for STCG slab)
    "property_cgt": {"stcg_rate_pct": 30, "ltcg_rate_pct": 20, "holding_days_ltcg": 730},
    # Cost Inflation Index (add/update as needed)
    "cii": {
        "FY2019-20": 289, "FY2020-21": 301, "FY2021-22": 317, "FY2022-23": 331,
        "FY2023-24": 348, "FY2024-25": 348, "FY2025-26": 360
    },
}

def engine_with_tax(tax: Dict | None = None) -> TimelineEngine:
    ctx = EngineContext()
    ctx.tax_laws_fy = MiniTaxCfg(content=(tax or DEFAULT_TAX))
    return TimelineEngine(ctx)

def run_engine(portfolio):
    eng = engine_with_tax()
    eng.ctx.portfolio = portfolio
    eng.run()
    return eng.result()
