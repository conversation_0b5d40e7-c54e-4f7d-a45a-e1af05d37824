from pydantic import BaseModel, Field, validator
from ..types import Money, Percent, D
from .enums import KhataType, OCStatus, LandTenure

class LegalProfile(BaseModel):
    khata_type: KhataType = KhataType.A
    oc_status: OCStatus = OCStatus.GRANTED
    cc_status: str = "NA"
    land_tenure: LandTenure = LandTenure.FREEHOLD
    annual_ground_rent: Money | None = None
    lease_years_remaining: int | None = None
    encumbrance_flag: bool = False
    b_khata_discount_pct: Percent = Field(default=D(0))
    oc_risk_discount_pct: Percent = Field(default=D(0))
    bba_buyer_late_penalty_interest_pct_annual: Percent = Field(default=D(18))
    buyer_late_penalty_grace_days: int = Field(default=7, ge=0, le=60)
