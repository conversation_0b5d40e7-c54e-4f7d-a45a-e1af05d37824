from decimal import Decimal as D
from rede.types import D as DD
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import (
    Property, PropertyHeads, PaymentPlan, PaymentEvent, PaymentTrigger,
    PropertyOwnershipCosts, PropertyOccupancy
)
from rede.models.loans import TermLoan, RateShock
from rede.models.enums import PaymentTriggerType
from .helpers import run_engine

def test_s5_rate_shocks_policy():
    u = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(1_500_000))
    ev = PaymentEvent(name="Full 100%", stage="Buy", trigger=PaymentTrigger(type=PaymentTriggerType.month, month=0),
                      percent_of_base=DD(100), gst_pct=DD(0), financeable_by_bank=True)
    loan = TermLoan(
        principal_sanctioned=DD(1_000_000), tenure_months=120,
        emi_start_month=1, # EMI from next month
        initial_roi_annual_pct=DD(9),
        on_rate_change="adjust_emi",
        rate_shocks=[RateShock(month=12, new_index_pct=DD(9.75)), RateShock(month=24, new_index_pct=DD(9.25))]
    )
    prop = Property(
        id="L1", city="Gurgaon",
        heads=PropertyHeads(base_price_ex_gst=DD(1_000_000), tds_buy_pct=DD(1), ltv_pct=DD(75)),
        plan=PaymentPlan(events=[ev]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(),
        loan=loan
    )
    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=30,
        persons=[u], properties=[prop], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(1_500_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(), tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )
    res = run_engine(pf)
    narr = res["narrative_summary"]
    assert any("Loan rate shock applied" in s for s in narr), "Expected rate shock narratives"
    # EMI should be posted; at least one EMI line must exist
    assert any(l for l in res["ledger"] if l["category"] == "emi"), "Expected EMI postings"
