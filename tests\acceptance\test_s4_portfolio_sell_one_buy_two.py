from decimal import Decimal as D
from rede.types import D as DD
from rede.models.core import Portfolio, Person, FundingStrategy, RuntimeConfigs, TaxSettingsGlobal
from rede.models.property import (
    Property, PropertyHeads, PaymentPlan, PaymentEvent, PaymentTrigger,
    PropertyOwnershipCosts, PropertyOccupancy, ExitSettings
)
from rede.models.enums import PaymentTriggerType
from .helpers import run_engine

def test_s4_portfolio_sell_one_buy_two():
    u = Person(id="u", name="User", tax_regime="old", shareholdings={}, liquid_cash=DD(1_000_000))

    # Asset A: RTM, sell at M12 to fund B & C
    A = Property(
        id="A", city="Pune",
        heads=PropertyHeads(base_price_ex_gst=DD(5_000_000), tds_buy_pct=DD(1)),
        plan=PaymentPlan(events=[]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(),
        exit=ExitSettings(sale_month=12, brokerage_pct=DD(1), buyer_tds_pct=DD(1))
    )
    # Asset B: UC with 30/70 (possession at M24)
    B = Property(
        id="B", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(8_000_000), tds_buy_pct=DD(1), ltv_pct=DD(75)),
        plan=PaymentPlan(events=[
            PaymentEvent(name="30%", stage="Start", trigger=PaymentTrigger(type=PaymentTriggerType.month, month=13),
                         percent_of_base=DD(30), gst_pct=DD(5), financeable_by_bank=True),
            PaymentEvent(name="70%", stage="Possession", trigger=PaymentTrigger(type=PaymentTriggerType.month, month=24),
                         percent_of_base=DD(70), gst_pct=DD(5), financeable_by_bank=True),
        ]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(move_in_month=None, let_out_month=25)  # investor
    )
    # Asset C: UC with 20/80 (possession at M24)
    C = Property(
        id="C", city="BLR",
        heads=PropertyHeads(base_price_ex_gst=DD(6_000_000), tds_buy_pct=DD(1), ltv_pct=DD(75)),
        plan=PaymentPlan(events=[
            PaymentEvent(name="20%", stage="Start", trigger=PaymentTrigger(type=PaymentTriggerType.month, month=13),
                         percent_of_base=DD(20), gst_pct=DD(5), financeable_by_bank=True),
            PaymentEvent(name="80%", stage="Possession", trigger=PaymentTrigger(type=PaymentTriggerType.month, month=24),
                         percent_of_base=DD(80), gst_pct=DD(5), financeable_by_bank=True),
        ]),
        ownership_costs=PropertyOwnershipCosts(),
        occupancy=PropertyOccupancy(let_out_month=26)
    )

    pf = Portfolio(
        currency="INR", start_date="2025-04-05", posting_day_of_month=5, horizon_months=36,
        persons=[u], properties=[A, B, C], mf_holdings=[],
        user_starting_liquid_cash_by_person={"u": DD(1_000_000)},
        alt_invest_return_after_tax_pct=DD(6), loan_sanction_risk_pct=DD(0),
        funding_strategy=FundingStrategy(od_limit=DD(2_000_000), od_annual_rate_pct=DD(12), mf_fifo_enabled=False),
        tax_settings_global=TaxSettingsGlobal(), configs=RuntimeConfigs("","","","","")
    )

    res = run_engine(pf)
    # Expect: sale of A, builder payments for B & C, possible OD usage around possession months
    labels = [l["label"] for l in res["ledger"]]
    assert any("Sale Proceeds" in s for s in labels), "Expected A sale proceeds"
    assert any("Builder Payment" in s for s in labels), "Expected builder payments for B or C"
    # Risk/Narratives present
    assert res["narrative_summary"], "Narratives should be present"
