class NarrativeEngine:
    """
    Produces narrative_summary (top 5 sentences) and key_events per v1.4 §10.
    The current Timeline writes key one-liners (e.g., rate shocks) directly.
    Hook this later to generate richer text from KPIs & flags.
    """

    def summarize(self, calc_result) -> list[str]:
        raise NotImplementedError

    def key_events(self, calc_result) -> list[dict]:
        raise NotImplementedError
