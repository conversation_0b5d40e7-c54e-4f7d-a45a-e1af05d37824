from __future__ import annotations
from decimal import Decimal, getcontext
from datetime import date
from ..types import Money, Percent, D
from ..models.property import Property
from ..models.loans import BaseLoan, TermLoan, OverdraftLoan, RateIndex, RateChangePolicy, LTVBasis
from ..engine.context import EngineContext
from ..utils.money import pct_to_decimal

getcontext().prec = 28

def _agreement_base_ex_gst(prop: Property) -> Decimal:
    h = prop.heads
    return D(h.base_price_ex_gst) + D(h.plc_floor_rise) + D(h.parking_charges) + D(h.club_membership) + D(h.other_capital_heads)

class LoanEngine:
    """
    v1.4 §5 — disbursals (LTV + stage caps), pre-EMI/EMI, floating resets, MRTA bundling.
    No external rate-index dependency: we honor loan.rate_shocks and loan.reset_frequency policy;
    if index data is later wired, you can feed ctx.rate_index_path.content.
    """

    # ---------- Rate handling ----------
    def monthly_rate(self, ctx: EngineContext, loan: BaseLoan, as_of: date, t: int) -> Decimal:
        # Fixed → simple
        if loan.rate_index == RateIndex.FIXED:
            return pct_to_decimal(Percent(D(loan.initial_roi_annual_pct))) / D(12)

        # Floating path: prefer explicit shocks; else use last known index or fall back to initial
        idx_annual = None

        # Apply explicit shock in month t if present
        for rs in (loan.rate_shocks or []):
            if rs.month == t:
                loan.current_index_pct = float(rs.new_index_pct)  # store for traceability
                idx_annual = Decimal(loan.current_index_pct)
                break

        if idx_annual is None:
            # Retain previously set index, or approximate from initial - spread
            if hasattr(loan, "current_index_pct") and loan.current_index_pct is not None:
                idx_annual = Decimal(loan.current_index_pct)
            else:
                idx_annual = D(loan.initial_roi_annual_pct) - D(loan.spread_pct)

        annual = idx_annual + D(loan.spread_pct)
        return pct_to_decimal(Percent(annual)) / D(12)

    # ---------- Disbursal ----------
    def compute_max_loan_cap(self, prop: Property) -> Decimal:
        B = _agreement_base_ex_gst(prop)
        ltv_basis = prop.ltv_basis or (prop.loan.ltv_basis if prop.loan else None)
        if not ltv_basis:
            return D(0)
        ltv_cap = pct_to_decimal(Percent(D(ltv_basis.ltv_pct))) * B
        return ltv_cap

    def stage_cap_remaining(self, prop: Property, stage_name: str, bank_paid_so_far: Decimal) -> Decimal:
        if not prop.bank_stage_caps:
            return Decimal("1e18")
        B = _agreement_base_ex_gst(prop)
        for cap in prop.bank_stage_caps:
            if cap.stage_name == stage_name:
                max_allowed = pct_to_decimal(Percent(D(cap.max_pct_of_agreement_ex_gst))) * B
                return max(Decimal("0"), max_allowed - bank_paid_so_far)
        return Decimal("1e18")

    def compute_disbursal(self, ctx: EngineContext, prop: Property, stage_name: str, financeable_demand: Money, bank_paid_so_far: Money) -> Money:
        """
        disbursal = min(Δ_s, MaxLoan - bank_paid, StageCap_s*B - bank_paid)
        """
        if prop.loan is None:
            return Money(D(0))
        max_loan = self.compute_max_loan_cap(prop)
        headroom = max(Decimal("0"), max_loan - D(bank_paid_so_far))
        stage_room = self.stage_cap_remaining(prop, stage_name, D(bank_paid_so_far))
        allowed = min(D(financeable_demand), headroom, stage_room)
        return Money(max(Decimal("0"), allowed))

    # ---------- EMI / pre-EMI ----------
    def compute_preemi(self, outstanding: Money, roi_monthly: Decimal) -> Money:
        return Money(D(outstanding) * roi_monthly)

    def compute_emi(self, principal: Money, roi_monthly: Decimal, tenure_months: int) -> Money:
        P = D(principal)
        i = roi_monthly
        n = Decimal(tenure_months)
        if i == 0:
            return Money(P / n)
        num = P * i * (1 + i) ** n
        den = (1 + i) ** n - 1
        return Money(num / den)

    def solve_tenure_for_new_rate(self, emi: Money, principal: Money, roi_monthly: Decimal) -> int:
        # n = -ln(1 - i*P/EMI) / ln(1+i)
        P = D(principal); i = roi_monthly; E = D(emi)
        if i == 0:
            return int((P / E).to_integral_value(rounding="ROUND_CEILING"))
        x = 1 - i * P / E
        if x <= 0:
            return 1
        import math
        n = -math.log(float(x)) / math.log(float(1 + i))
        return max(1, int(n + 0.9999))  # ceiling

    def on_rate_change(self, loan: BaseLoan, principal_remaining: Money, new_rate_monthly: Decimal):
        """
        ADJUST_TENURE: keep EMI, recompute remaining tenure.
        ADJUST_EMI:   keep tenure, recompute EMI.
        """
        if loan.on_rate_change_policy == RateChangePolicy.ADJUST_TENURE:
            n_new = self.solve_tenure_for_new_rate(loan.current_emi, principal_remaining, new_rate_monthly)
            loan.tenure_months = n_new
        else:
            loan.current_emi = self.compute_emi(principal_remaining, new_rate_monthly, loan.tenure_months)

    # ---------- MRTA impact ----------
    def apply_mrta_upfront(self, loan: BaseLoan, principal_at_sanction: Money) -> Money:
        if loan.loan_insurance and getattr(loan.loan_insurance, "premium_payment", None) == "UpfrontBundled":
            premium = D(loan.loan_insurance.upfront_premium or 0)
            return Money(D(principal_at_sanction) + premium)
        return principal_at_sanction

    def maybe_apply_rate_shock(self, loan: BaseLoan, t: int, posting_date: date, principal_remaining: Money):
        """
        If a shock is defined for this month, set new index and apply on_rate_change policy.
        """
        for rs in (loan.rate_shocks or []):
            if rs.month == t:
                loan.current_index_pct = float(rs.new_index_pct)
                new_rm = pct_to_decimal(Percent(D(rs.new_index_pct) + D(loan.spread_pct))) / D(12)
                if not hasattr(loan, "current_emi") or loan.current_emi is None:
                    loan.current_emi = self.compute_emi(principal_remaining, new_rm, loan.tenure_months)
                    return
                self.on_rate_change(loan, principal_remaining, new_rm)
                return
