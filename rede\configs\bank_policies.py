from __future__ import annotations
from pydantic import BaseModel, Field
from typing import Dict, Literal
from decimal import Decimal

Percent = Decimal

class LTVPolicy(BaseModel):
    ltv_pct: Percent = Decimal("75.0")
    exclude_gst_from_base: bool = True

class StageCapPolicy(BaseModel):
    # stage name -> max % of agreement ex-GST bank will disburse cumulatively up to that stage
    caps_pct: Dict[str, Percent] = Field(default_factory=dict)

class SanctionPolicy(BaseModel):
    validity_months: int = 6
    typical_sanction_cut_risk_pct: Percent = Decimal("0.0")  # e.g., 10 => expect 10% lower than applied

class TopupPolicy(BaseModel):
    allow_on_under_construction: bool = False
    max_ltv_pct_on_topup: Percent = Decimal("70.0")

class PrepaymentCharges(BaseModel):
    foreclosure_penalty_pct: Percent = Decimal("0.0")
    part_prepayment_penalty_pct: Percent = Decimal("0.0")

class ODPolicy(BaseModel):
    allowed: bool = True
    typical_spread_over_index_pct: Percent = Decimal("0.0")
    note: str = "Model only — use actual sanction doc."

class BankPolicies(BaseModel):
    ltv: LTVPolicy = LTVPolicy()
    stage_caps: StageCapPolicy = StageCapPolicy()
    sanction: SanctionPolicy = SanctionPolicy()
    topup: TopupPolicy = TopupPolicy()
    prepayment: PrepaymentCharges = PrepaymentCharges()
    overdraft: ODPolicy = ODPolicy()

def parse_bank_policies(content: dict) -> BankPolicies:
    if not isinstance(content, dict):
        content = {}
    return BankPolicies.parse_obj(content)
