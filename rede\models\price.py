from pydantic import BaseModel, Field
from ..types import Percent, D

class Shock(BaseModel):
    start_month: int
    end_month: int
    cumulative_pct: Percent  # can be negative

class AgeDiscount(BaseModel):
    age_year: int
    discount_pct: Percent

class PriceModel(BaseModel):
    base_annual_cagr_pct: Percent
    shocks: list[Shock] = Field(default_factory=list)
    age_factor_curve: list[AgeDiscount] | None = None
    interiors_in_value: bool = False
    interiors_depr_years: int = 10
    interiors_residual_pct: Percent = D(20)
