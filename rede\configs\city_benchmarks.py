from __future__ import annotations
from pydantic import BaseModel, <PERSON>
from typing import Dict, Literal
from decimal import Decimal

Percent = Decimal

class RentBench(BaseModel):
    rent_yield_pct_of_value: Percent = Decimal("3.0")    # % of market value p.a.
    vacancy_pct: Percent = Decimal("5.0")
    collection_efficiency_pct: Percent = Decimal("98.0")
    annual_rent_escalation_pct: Percent = Decimal("5.0")

class CostBench(BaseModel):
    maintenance_per_sqft_month: Decimal = Decimal("3.0")
    maintenance_escalation_pct: Percent = Decimal("7.0")

class PriceBench(BaseModel):
    base_appreciation_cagr_pct: Percent = Decimal("5.0")

class SegmentBenchmark(BaseModel):
    rent: RentBench = RentBench()
    costs: CostBench = CostBench()
    price: PriceBench = PriceBench()

class CityBenchmarks(BaseModel):
    """
    city -> segment -> SegmentBenchmark
    segments might include: 'Affordable', 'Mid', 'Luxury'
    """
    cities: Dict[str, Dict[str, SegmentBenchmark]] = Field(default_factory=dict)

def parse_city_benchmarks(content: dict) -> CityBenchmarks:
    if not isinstance(content, dict):
        content = {}
    if "cities" not in content:
        # allow raw {city: {segment: {...}}}
        content = {"cities": content}
    return CityBenchmarks.parse_obj(content)
